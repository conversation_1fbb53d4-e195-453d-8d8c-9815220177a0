<template>
  <div class="search-page">
    <el-container>
      <!-- 页面头部 -->
      <el-header class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <el-icon><Search /></el-icon>
            搜索结果
          </h1>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ name: 'Home' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>搜索结果</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="search-main">
        <!-- 搜索栏 -->
        <el-card class="search-card" shadow="never">
          <div class="search-bar">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入商品名称"
              size="large"
              class="search-input"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
              <template #append>
                <el-button type="primary" @click="handleSearch">
                  搜索
                </el-button>
              </template>
            </el-input>
          </div>
          
          <!-- 热门搜索 -->
          <div class="hot-keywords" v-if="hotKeywords.length > 0">
            <span class="hot-label">热门搜索：</span>
            <el-tag
              v-for="keyword in hotKeywords"
              :key="keyword"
              class="hot-tag"
              @click="searchKeyword = keyword; handleSearch()"
            >
              {{ keyword }}
            </el-tag>
          </div>
        </el-card>

        <!-- 搜索结果 -->
        <div class="search-results">
          <!-- 结果统计 -->
          <div class="result-stats" v-if="searchResults.length > 0">
            <span>找到 <strong>{{ total }}</strong> 件相关商品</span>
            <span class="search-time">搜索用时: {{ searchTime }}ms</span>
          </div>

          <!-- 筛选和排序 -->
          <el-card class="filter-card" shadow="never" v-if="searchResults.length > 0">
            <div class="filter-bar">
              <div class="filter-section">
                <span class="filter-label">价格区间：</span>
                <el-button-group class="price-filters">
                  <el-button 
                    v-for="range in priceRanges"
                    :key="range.value"
                    :type="selectedPriceRange === range.value ? 'primary' : 'default'"
                    size="small"
                    @click="selectPriceRange(range.value)"
                  >
                    {{ range.label }}
                  </el-button>
                </el-button-group>
              </div>
              
              <div class="sort-section">
                <span class="sort-label">排序：</span>
                <el-select v-model="sortBy" size="small" style="width: 120px" @change="handleSort">
                  <el-option label="综合" value="default" />
                  <el-option label="价格升序" value="price_asc" />
                  <el-option label="价格降序" value="price_desc" />
                  <el-option label="销量" value="sales" />
                </el-select>
              </div>
            </div>
          </el-card>

          <!-- 商品列表 -->
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="6" animated />
          </div>
          
          <div v-else-if="searchResults.length > 0" class="goods-grid">
            <el-card 
              v-for="goods in searchResults" 
              :key="goods.id"
              class="goods-card"
              shadow="hover"
              @click="goToDetail(goods.id)"
            >
              <template #header>
                <div class="goods-image-container">
                  <el-image 
                    :src="goods.picture" 
                    :alt="goods.name" 
                    class="goods-image"
                    fit="cover"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
              </template>
              
              <div class="goods-content">
                <h3 class="goods-name" v-html="highlightKeyword(goods.name)"></h3>
                
                <div class="goods-price-section">
                  <span class="goods-price">¥{{ goods.price }}</span>
                  <span class="goods-sales">已售{{ goods.sales || 0 }}件</span>
                </div>
                
                <p class="goods-description" v-html="highlightKeyword(goods.description)"></p>
                
                <div class="goods-footer">
                  <div class="goods-stock">
                    <el-icon><Box /></el-icon>
                    库存: {{ goods.stock || 0 }}
                  </div>
                  
                  <el-button 
                    type="primary" 
                    size="small"
                    @click.stop="addToCart(goods)"
                    :disabled="!goods.stock"
                  >
                    <el-icon><ShoppingCart /></el-icon>
                    加入购物车
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>
          
          <!-- 空状态 -->
          <el-empty v-else-if="!loading && hasSearched" description="没有找到相关商品">
            <el-button type="primary" @click="$router.push('/')">
              浏览全部商品
            </el-button>
          </el-empty>
          
          <!-- 初始状态 -->
          <div v-else-if="!hasSearched" class="initial-state">
            <el-empty description="请输入关键词搜索商品">
              <div class="suggestions">
                <h4>推荐搜索：</h4>
                <div class="suggestion-tags">
                  <el-tag
                    v-for="suggestion in suggestions"
                    :key="suggestion"
                    class="suggestion-tag"
                    @click="searchKeyword = suggestion; handleSearch()"
                  >
                    {{ suggestion }}
                  </el-tag>
                </div>
              </div>
            </el-empty>
          </div>

          <!-- 分页 -->
          <el-pagination
            v-if="total > 0"
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[12, 24, 48]"
            layout="total, sizes, prev, pager, next, jumper"
            class="pagination"
            @size-change="handleSearch"
            @current-change="handleSearch"
          />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()

// 响应式数据
const searchKeyword = ref('')
const searchResults = ref([])
const loading = ref(false)
const hasSearched = ref(false)
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)
const searchTime = ref(0)
const sortBy = ref('default')
const selectedPriceRange = ref('')

// 静态数据
const hotKeywords = ref(['苹果', '香蕉', '牛奶', '面包', '鸡蛋'])
const suggestions = ref(['新鲜水果', '有机蔬菜', '进口零食', '健康饮品'])
const priceRanges = ref([
  { label: '全部', value: '' },
  { label: '0-50', value: '0-50' },
  { label: '50-100', value: '50-100' },
  { label: '100-200', value: '100-200' },
  { label: '200以上', value: '200+' }
])

// 生命周期
onMounted(() => {
  // 从路由参数获取搜索关键词
  if (route.query.q) {
    searchKeyword.value = route.query.q
    handleSearch()
  }
})

// 方法
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  loading.value = true
  hasSearched.value = true
  const startTime = Date.now()
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟搜索结果
    searchResults.value = [
      {
        id: 1,
        name: '新鲜苹果',
        picture: 'https://via.placeholder.com/200',
        price: 29.99,
        description: '新鲜甜美的红富士苹果，营养丰富',
        stock: 100,
        sales: 256
      },
      {
        id: 2,
        name: '有机香蕉',
        picture: 'https://via.placeholder.com/200',
        price: 19.99,
        description: '进口有机香蕉，口感香甜',
        stock: 80,
        sales: 189
      }
    ]
    
    total.value = searchResults.value.length
    searchTime.value = Date.now() - startTime
    
    // 更新URL
    router.replace({ query: { q: searchKeyword.value } })
  } catch (error) {
    ElMessage.error('搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

const selectPriceRange = (range) => {
  selectedPriceRange.value = range
  handleSearch()
}

const handleSort = () => {
  handleSearch()
}

const highlightKeyword = (text) => {
  if (!searchKeyword.value || !text) return text
  const regex = new RegExp(`(${searchKeyword.value})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const goToDetail = (goodsId) => {
  router.push(`/goods/${goodsId}`)
}

const addToCart = (goods) => {
  ElMessage.success('已添加到购物车')
}
</script>

<style scoped>
.search-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 80px !important;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.search-card {
  margin-bottom: 24px;
}

.search-bar {
  margin-bottom: 16px;
}

.search-input {
  max-width: 600px;
}

.hot-keywords {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.hot-label {
  color: #606266;
  font-size: 14px;
}

.hot-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.hot-tag:hover {
  background: #409eff;
  color: white;
}

.result-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
}

.search-time {
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-section,
.sort-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label,
.sort-label {
  color: #606266;
  font-size: 14px;
}

.goods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.goods-card {
  cursor: pointer;
  transition: all 0.3s;
}

.goods-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.goods-image-container {
  height: 200px;
  overflow: hidden;
}

.goods-image {
  width: 100%;
  height: 100%;
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f5f7fa;
  color: #c0c4cc;
  font-size: 24px;
}

.goods-content {
  padding: 16px;
}

.goods-name {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-name :deep(mark) {
  background: #fff3cd;
  color: #856404;
  padding: 2px 4px;
  border-radius: 2px;
}

.goods-price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.goods-price {
  font-size: 20px;
  font-weight: 700;
  color: #f56c6c;
}

.goods-sales {
  font-size: 12px;
  color: #909399;
}

.goods-description {
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-description :deep(mark) {
  background: #fff3cd;
  color: #856404;
  padding: 2px 4px;
  border-radius: 2px;
}

.goods-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-stock {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.loading-container {
  padding: 40px;
}

.initial-state {
  text-align: center;
  padding: 40px;
}

.suggestions {
  margin-top: 20px;
}

.suggestions h4 {
  margin: 0 0 12px 0;
  color: #606266;
}

.suggestion-tags {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.suggestion-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.suggestion-tag:hover {
  background: #409eff;
  color: white;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .result-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .goods-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }
  
  .hot-keywords {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
