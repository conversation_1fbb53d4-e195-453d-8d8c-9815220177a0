<template>
  <div class="checkout-page">
    <el-container>
      <!-- 页面头部 -->
      <el-header class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <el-icon><CreditCard /></el-icon>
            确认订单
          </h1>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ name: 'Home' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ name: 'Cart' }">购物车</el-breadcrumb-item>
            <el-breadcrumb-item>确认订单</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="checkout-main">
        <el-row :gutter="24">
          <!-- 左侧订单信息 -->
          <el-col :xs="24" :lg="16">
            <!-- 收货地址 -->
            <el-card class="address-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>收货地址</span>
                  <el-button type="text" @click="addressVisible = true">
                    管理地址
                  </el-button>
                </div>
              </template>
              
              <div v-if="selectedAddress" class="selected-address">
                <div class="address-info">
                  <div class="recipient">
                    <span class="name">{{ selectedAddress.name }}</span>
                    <span class="phone">{{ selectedAddress.phone }}</span>
                  </div>
                  <div class="address-detail">{{ selectedAddress.detail }}</div>
                </div>
                <el-button type="text" @click="addressVisible = true">
                  更换地址
                </el-button>
              </div>
              
              <el-empty v-else description="请选择收货地址">
                <el-button type="primary" @click="addressVisible = true">
                  添加地址
                </el-button>
              </el-empty>
            </el-card>

            <!-- 商品清单 -->
            <el-card class="goods-card" shadow="hover">
              <template #header>
                <span>商品清单</span>
              </template>
              
              <div class="goods-list">
                <div 
                  v-for="item in cartItems" 
                  :key="item.id"
                  class="goods-item"
                >
                  <el-image 
                    :src="item.goods.picture" 
                    class="goods-image"
                    fit="cover"
                  />
                  <div class="goods-info">
                    <h4 class="goods-name">{{ item.goods.name }}</h4>
                    <p class="goods-spec">{{ item.goods.spec }}</p>
                    <div class="goods-price-qty">
                      <span class="price">¥{{ item.goods.price }}</span>
                      <span class="quantity">x{{ item.quantity }}</span>
                    </div>
                  </div>
                  <div class="item-total">
                    ¥{{ (item.goods.price * item.quantity).toFixed(2) }}
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 支付方式 -->
            <el-card class="payment-card" shadow="hover">
              <template #header>
                <span>支付方式</span>
              </template>
              
              <el-radio-group v-model="paymentMethod" class="payment-methods">
                <el-radio value="alipay" class="payment-option">
                  <div class="payment-content">
                    <el-icon class="payment-icon alipay"><Money /></el-icon>
                    <span>支付宝</span>
                  </div>
                </el-radio>
                <el-radio value="wechat" class="payment-option">
                  <div class="payment-content">
                    <el-icon class="payment-icon wechat"><ChatDotRound /></el-icon>
                    <span>微信支付</span>
                  </div>
                </el-radio>
                <el-radio value="bank" class="payment-option">
                  <div class="payment-content">
                    <el-icon class="payment-icon bank"><CreditCard /></el-icon>
                    <span>银行卡</span>
                  </div>
                </el-radio>
              </el-radio-group>
            </el-card>
          </el-col>

          <!-- 右侧订单摘要 -->
          <el-col :xs="24" :lg="8">
            <el-card class="summary-card" shadow="hover">
              <template #header>
                <span>订单摘要</span>
              </template>
              
              <div class="summary-content">
                <div class="summary-item">
                  <span>商品总价</span>
                  <span>¥{{ subtotal.toFixed(2) }}</span>
                </div>
                <div class="summary-item">
                  <span>运费</span>
                  <span>¥{{ shipping.toFixed(2) }}</span>
                </div>
                <div class="summary-item discount">
                  <span>优惠</span>
                  <span>-¥{{ discount.toFixed(2) }}</span>
                </div>
                <el-divider />
                <div class="summary-item total">
                  <span>应付总额</span>
                  <span>¥{{ total.toFixed(2) }}</span>
                </div>
              </div>
              
              <el-button 
                type="primary" 
                size="large" 
                class="submit-btn"
                :disabled="!selectedAddress || !paymentMethod"
                @click="submitOrder"
                :loading="submitting"
              >
                {{ submitting ? '提交中...' : '提交订单' }}
              </el-button>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>

    <!-- 地址选择对话框 -->
    <el-dialog v-model="addressVisible" title="选择收货地址" width="600px">
      <div class="address-list">
        <div 
          v-for="address in addresses" 
          :key="address.id"
          class="address-item"
          :class="{ selected: selectedAddress?.id === address.id }"
          @click="selectAddress(address)"
        >
          <div class="address-info">
            <div class="recipient">
              <span class="name">{{ address.name }}</span>
              <span class="phone">{{ address.phone }}</span>
            </div>
            <div class="address-detail">{{ address.detail }}</div>
          </div>
          <el-icon v-if="selectedAddress?.id === address.id" class="selected-icon">
            <Check />
          </el-icon>
        </div>
      </div>
      <template #footer>
        <el-button @click="addressVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddress">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()

// 响应式数据
const addressVisible = ref(false)
const selectedAddress = ref(null)
const paymentMethod = ref('alipay')
const submitting = ref(false)

// 模拟数据
const cartItems = ref([
  {
    id: 1,
    goods: {
      name: '新鲜苹果',
      picture: 'https://via.placeholder.com/100',
      spec: '5kg装',
      price: 99.99
    },
    quantity: 2
  }
])

const addresses = ref([
  {
    id: 1,
    name: '张三',
    phone: '13800138000',
    detail: '北京市朝阳区xxx街道xxx号'
  },
  {
    id: 2,
    name: '李四',
    phone: '13900139000',
    detail: '上海市浦东新区xxx路xxx号'
  }
])

// 计算属性
const subtotal = computed(() => {
  return cartItems.value.reduce((sum, item) => {
    return sum + (item.goods.price * item.quantity)
  }, 0)
})

const shipping = computed(() => {
  return subtotal.value > 99 ? 0 : 10
})

const discount = computed(() => {
  return 0 // 暂无优惠
})

const total = computed(() => {
  return subtotal.value + shipping.value - discount.value
})

// 生命周期
onMounted(() => {
  // 默认选择第一个地址
  if (addresses.value.length > 0) {
    selectedAddress.value = addresses.value[0]
  }
})

// 方法
const selectAddress = (address) => {
  selectedAddress.value = address
}

const confirmAddress = () => {
  addressVisible.value = false
}

const submitOrder = async () => {
  if (!selectedAddress.value) {
    ElMessage.warning('请选择收货地址')
    return
  }
  
  if (!paymentMethod.value) {
    ElMessage.warning('请选择支付方式')
    return
  }
  
  try {
    submitting.value = true
    
    // 模拟提交订单
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('订单提交成功！')
    router.push('/orders')
  } catch (error) {
    ElMessage.error('订单提交失败，请重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.checkout-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 80px !important;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkout-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.address-card,
.goods-card,
.payment-card,
.summary-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-address {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.address-info {
  flex: 1;
}

.recipient {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.name {
  font-weight: 600;
  color: #303133;
}

.phone {
  color: #606266;
}

.address-detail {
  color: #606266;
  font-size: 14px;
}

.goods-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.goods-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
}

.goods-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.goods-name {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.goods-spec {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.goods-price-qty {
  display: flex;
  gap: 16px;
  margin-top: auto;
}

.price {
  font-weight: 600;
  color: #f56c6c;
}

.quantity {
  color: #909399;
}

.item-total {
  font-size: 18px;
  font-weight: 600;
  color: #f56c6c;
  align-self: center;
}

.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.payment-option {
  width: 100%;
  margin: 0;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s;
}

.payment-option:hover {
  border-color: #409eff;
}

.payment-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.payment-icon {
  font-size: 24px;
}

.payment-icon.alipay { color: #1677ff; }
.payment-icon.wechat { color: #07c160; }
.payment-icon.bank { color: #f56c6c; }

.summary-content {
  margin-bottom: 24px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.summary-item.discount {
  color: #67c23a;
}

.summary-item.total {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.submit-btn {
  width: 100%;
}

.address-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.address-item:hover {
  border-color: #409eff;
}

.address-item.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.selected-icon {
  color: #409eff;
  font-size: 20px;
}

@media (max-width: 768px) {
  .goods-item {
    flex-direction: column;
    text-align: center;
  }
  
  .item-total {
    align-self: stretch;
    text-align: right;
  }
  
  .payment-methods {
    gap: 12px;
  }
  
  .payment-option {
    padding: 12px;
  }
}
</style>
