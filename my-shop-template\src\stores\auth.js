import { defineStore } from 'pinia'
import request from '../utils/request'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    user: null,
    isAuthenticated: false,
    initialized: false
  }),

  getters: {
    isLoggedIn: (state) => !!state.token && state.isAuthenticated
  },

  actions: {
    // 用户登录
    async login(credentials) {
      try {
        const response = await request.post('/home/<USER>', credentials)
        
        if (response.errno === 0) {
          this.token = response.data.token
          this.isAuthenticated = true
          
          // 存储token到localStorage
          localStorage.setItem('token', this.token)
          
          // 获取用户信息
          await this.fetchUser()
          
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('登录失败:', error)
        return { success: false, message: error.message || '登录失败' }
      }
    },

    // 用户注册
    async register(userData) {
      try {
        const response = await request.post('/home/<USER>', userData)
        
        if (response.errno === 0) {
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('注册失败:', error)
        return { success: false, message: error.message || '注册失败' }
      }
    },

    // 获取用户信息
    async fetchUser() {
      try {
        const response = await request.get('/home/<USER>')
        
        if (response.errno === 0) {
          this.user = response.data
          this.isAuthenticated = true
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.logout()
        return { success: false, message: error.message || '获取用户信息失败' }
      }
    },

    // 用户登出
    logout() {
      this.token = ''
      this.user = null
      this.isAuthenticated = false
      localStorage.removeItem('token')
    },

    // 初始化认证状态
    async initAuth() {
      if (this.token) {
        await this.fetchUser()
      }
      this.initialized = true
    }
  }
})
