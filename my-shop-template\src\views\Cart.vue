<template>
  <div class="cart-page">
    <div class="container">
      <h1 class="page-title">购物车</h1>
      
      <div v-if="goodsStore.cart.length === 0" class="empty-cart">
        <p>购物车是空的</p>
        <router-link to="/" class="continue-shopping">继续购物</router-link>
      </div>
      
      <div v-else class="cart-content">
        <div class="cart-items">
          <div 
            v-for="item in goodsStore.cart" 
            :key="item.id"
            class="cart-item"
          >
            <img :src="item.picture" :alt="item.name" class="item-image">
            
            <div class="item-info">
              <h3 class="item-name">{{ item.name }}</h3>
              <p class="item-price">¥{{ item.price }}</p>
            </div>
            
            <div class="quantity-controls">
              <button 
                @click="decreaseQuantity(item.id)"
                :disabled="item.quantity <= 1"
                class="quantity-btn"
              >
                -
              </button>
              <span class="quantity">{{ item.quantity }}</span>
              <button 
                @click="increaseQuantity(item.id)"
                class="quantity-btn"
              >
                +
              </button>
            </div>
            
            <div class="item-total">
              ¥{{ (item.price * item.quantity).toFixed(2) }}
            </div>
            
            <button 
              @click="removeItem(item.id)"
              class="remove-btn"
            >
              删除
            </button>
          </div>
        </div>
        
        <div class="cart-summary">
          <div class="summary-row">
            <span>商品总数：</span>
            <span>{{ goodsStore.cartCount }}件</span>
          </div>
          
          <div class="summary-row total">
            <span>总计：</span>
            <span class="total-price">¥{{ goodsStore.cartTotal.toFixed(2) }}</span>
          </div>
          
          <div class="cart-actions">
            <button @click="clearCart" class="clear-btn">清空购物车</button>
            <button @click="checkout" class="checkout-btn">去结算</button>
          </div>
        </div>
      </div>
      
      <div class="back-to-shop">
        <router-link to="/" class="back-link">← 继续购物</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useGoodsStore } from '../stores/goods'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const goodsStore = useGoodsStore()
const authStore = useAuthStore()

const decreaseQuantity = (itemId) => {
  const item = goodsStore.cart.find(item => item.id === itemId)
  if (item && item.quantity > 1) {
    goodsStore.updateCartQuantity(itemId, item.quantity - 1)
  }
}

const increaseQuantity = (itemId) => {
  const item = goodsStore.cart.find(item => item.id === itemId)
  if (item) {
    goodsStore.updateCartQuantity(itemId, item.quantity + 1)
  }
}

const removeItem = (itemId) => {
  if (confirm('确定要删除这件商品吗？')) {
    goodsStore.removeFromCart(itemId)
  }
}

const clearCart = () => {
  if (confirm('确定要清空购物车吗？')) {
    goodsStore.clearCart()
  }
}

const checkout = () => {
  if (!authStore.isLoggedIn) {
    if (confirm('请先登录后再结算，是否前往登录页面？')) {
      router.push('/login')
    }
    return
  }
  
  // 这里可以跳转到结算页面
  alert('结算功能开发中...')
}
</script>

<style scoped>
.cart-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-title {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
}

.empty-cart {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.empty-cart p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 1rem;
}

.continue-shopping {
  background: #007bff;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  display: inline-block;
}

.continue-shopping:hover {
  background: #0056b3;
}

.cart-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.cart-items {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cart-item {
  display: grid;
  grid-template-columns: 80px 1fr auto auto auto;
  gap: 1rem;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.item-name {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.item-price {
  color: #e74c3c;
  font-weight: bold;
  margin: 0;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantity-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
  font-size: 1rem;
}

.quantity-btn:hover:not(:disabled) {
  background: #f8f9fa;
}

.quantity-btn:disabled {
  background: #e9ecef;
  cursor: not-allowed;
  color: #6c757d;
}

.quantity {
  font-weight: 500;
  min-width: 32px;
  text-align: center;
}

.item-total {
  font-size: 1.1rem;
  font-weight: bold;
  color: #e74c3c;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.remove-btn:hover {
  background: #c82333;
}

.cart-summary {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  height: fit-content;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.summary-row:last-of-type {
  border-bottom: none;
  margin-bottom: 1.5rem;
}

.summary-row.total {
  font-size: 1.2rem;
  font-weight: bold;
}

.total-price {
  color: #e74c3c;
  font-size: 1.4rem;
}

.cart-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.clear-btn,
.checkout-btn {
  padding: 0.75rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clear-btn {
  background: #6c757d;
  color: white;
}

.clear-btn:hover {
  background: #5a6268;
}

.checkout-btn {
  background: #28a745;
  color: white;
  font-weight: bold;
}

.checkout-btn:hover {
  background: #218838;
}

.back-to-shop {
  margin-top: 2rem;
  text-align: center;
}

.back-link {
  color: #007bff;
  text-decoration: none;
  font-size: 1.1rem;
}

.back-link:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .cart-content {
    grid-template-columns: 1fr;
  }
  
  .cart-item {
    grid-template-columns: 60px 1fr;
    gap: 0.5rem;
  }
  
  .quantity-controls,
  .item-total,
  .remove-btn {
    grid-column: 1 / -1;
    justify-self: start;
    margin-top: 0.5rem;
  }
}
</style>
