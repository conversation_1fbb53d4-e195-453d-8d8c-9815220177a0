<template>
  <div class="orders-page">
    <el-container>
      <!-- 页面头部 -->
      <el-header class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <el-icon><List /></el-icon>
            我的订单
          </h1>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ name: 'Home' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>我的订单</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="orders-main">
        <!-- 订单筛选 -->
        <el-card class="filter-card" shadow="never">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="全部订单" name="all" />
            <el-tab-pane label="待付款" name="pending" />
            <el-tab-pane label="待发货" name="paid" />
            <el-tab-pane label="待收货" name="shipped" />
            <el-tab-pane label="已完成" name="completed" />
            <el-tab-pane label="已取消" name="cancelled" />
          </el-tabs>
        </el-card>

        <!-- 订单列表 -->
        <div class="orders-list">
          <el-card 
            v-for="order in orders" 
            :key="order.id" 
            class="order-card"
            shadow="hover"
          >
            <!-- 订单头部 -->
            <template #header>
              <div class="order-header">
                <div class="order-info">
                  <span class="order-number">订单号: {{ order.order_no }}</span>
                  <span class="order-date">{{ formatDate(order.created_at) }}</span>
                </div>
                <el-tag :type="getStatusType(order.status)">
                  {{ getStatusText(order.status) }}
                </el-tag>
              </div>
            </template>

            <!-- 订单商品 -->
            <div class="order-goods">
              <div 
                v-for="item in order.items" 
                :key="item.id"
                class="goods-item"
              >
                <el-image 
                  :src="item.goods.picture" 
                  class="goods-image"
                  fit="cover"
                />
                <div class="goods-info">
                  <h4 class="goods-name">{{ item.goods.name }}</h4>
                  <p class="goods-spec">{{ item.goods.spec }}</p>
                  <div class="goods-price-qty">
                    <span class="price">¥{{ item.price }}</span>
                    <span class="quantity">x{{ item.quantity }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 订单总价和操作 -->
            <div class="order-footer">
              <div class="order-total">
                <span class="total-label">订单总额:</span>
                <span class="total-amount">¥{{ order.total_amount }}</span>
              </div>
              <div class="order-actions">
                <el-button 
                  v-if="order.status === 'pending'" 
                  type="primary"
                  @click="payOrder(order)"
                >
                  立即付款
                </el-button>
                <el-button 
                  v-if="order.status === 'shipped'" 
                  type="success"
                  @click="confirmReceive(order)"
                >
                  确认收货
                </el-button>
                <el-button 
                  v-if="['pending', 'paid'].includes(order.status)"
                  type="danger"
                  @click="cancelOrder(order)"
                >
                  取消订单
                </el-button>
                <el-button @click="viewOrderDetail(order)">
                  查看详情
                </el-button>
              </div>
            </div>
          </el-card>

          <!-- 空状态 -->
          <el-empty 
            v-if="!loading && orders.length === 0" 
            description="暂无订单"
            class="empty-orders"
          >
            <el-button type="primary" @click="$router.push('/')">
              去购物
            </el-button>
          </el-empty>

          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="3" animated />
          </div>
        </div>

        <!-- 分页 -->
        <el-pagination
          v-if="total > 0"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          class="pagination"
          @size-change="fetchOrders"
          @current-change="fetchOrders"
        />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const activeTab = ref('all')
const orders = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 生命周期
onMounted(() => {
  fetchOrders()
})

// 方法
const fetchOrders = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟订单数据
    orders.value = [
      {
        id: 1,
        order_no: 'ORD202501100001',
        status: 'pending',
        total_amount: 99.99,
        created_at: '2025-01-10 10:30:00',
        items: [
          {
            id: 1,
            goods: {
              name: '新鲜苹果',
              picture: 'https://via.placeholder.com/100',
              spec: '5kg装'
            },
            price: 99.99,
            quantity: 1
          }
        ]
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

const handleTabChange = (tab) => {
  activeTab.value = tab
  currentPage.value = 1
  fetchOrders()
}

const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'info',
    shipped: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待付款',
    paid: '待发货',
    shipped: '待收货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知状态'
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}

const payOrder = (order) => {
  ElMessage.info('跳转到支付页面...')
}

const confirmReceive = async (order) => {
  try {
    await ElMessageBox.confirm('确认收到商品了吗？', '确认收货', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('确认收货成功')
    fetchOrders()
  } catch {
    // 用户取消
  }
}

const cancelOrder = async (order) => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '取消订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('订单已取消')
    fetchOrders()
  } catch {
    // 用户取消
  }
}

const viewOrderDetail = (order) => {
  ElMessage.info('查看订单详情功能开发中...')
}
</script>

<style scoped>
.orders-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 80px !important;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.orders-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.filter-card {
  margin-bottom: 24px;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-card {
  border-radius: 12px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.order-number {
  font-weight: 600;
  color: #303133;
}

.order-date {
  font-size: 14px;
  color: #909399;
}

.order-goods {
  margin: 16px 0;
}

.goods-item {
  display: flex;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f2f5;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.goods-name {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.goods-spec {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.price {
  font-size: 16px;
  font-weight: 600;
  color: #f56c6c;
}

.quantity {
  color: #909399;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f0f2f5;
}

.order-total {
  display: flex;
  align-items: center;
  gap: 8px;
}

.total-label {
  color: #606266;
}

.total-amount {
  font-size: 18px;
  font-weight: 600;
  color: #f56c6c;
}

.order-actions {
  display: flex;
  gap: 8px;
}

.empty-orders {
  margin: 40px 0;
}

.loading-container {
  padding: 40px;
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .order-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .order-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
