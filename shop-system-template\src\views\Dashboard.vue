<template>
  <div class="dashboard">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <h2 class="logo">商城管理</h2>
      </div>
      
      <nav class="sidebar-nav">
        <router-link to="/" class="nav-item" exact-active-class="active">
          <span class="nav-icon">📊</span>
          <span class="nav-text">仪表板</span>
        </router-link>
        
        <router-link to="/goods" class="nav-item" active-class="active">
          <span class="nav-icon">📦</span>
          <span class="nav-text">商品管理</span>
        </router-link>
        
        <router-link to="/categories" class="nav-item" active-class="active">
          <span class="nav-icon">📂</span>
          <span class="nav-text">分类管理</span>
        </router-link>
        
        <router-link to="/profile" class="nav-item" active-class="active">
          <span class="nav-icon">👤</span>
          <span class="nav-text">个人资料</span>
        </router-link>
      </nav>
    </aside>
    
    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 顶部栏 -->
      <header class="top-bar">
        <div class="top-bar-left">
          <h1 class="page-title">仪表板</h1>
        </div>
        
        <div class="top-bar-right">
          <div class="admin-info">
            <span class="admin-name">{{ authStore.admin?.username || '管理员' }}</span>
            <button @click="logout" class="logout-btn">退出登录</button>
          </div>
        </div>
      </header>
      
      <!-- 内容区域 -->
      <div class="content">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">📦</div>
            <div class="stat-info">
              <h3 class="stat-number">{{ goodsStore.goodsList.length }}</h3>
              <p class="stat-label">商品总数</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">📂</div>
            <div class="stat-info">
              <h3 class="stat-number">{{ goodsStore.categories.length }}</h3>
              <p class="stat-label">分类总数</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">📈</div>
            <div class="stat-info">
              <h3 class="stat-number">{{ totalStock }}</h3>
              <p class="stat-label">总库存</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">💰</div>
            <div class="stat-info">
              <h3 class="stat-number">¥{{ totalValue.toFixed(2) }}</h3>
              <p class="stat-label">商品总价值</p>
            </div>
          </div>
        </div>
        
        <div class="recent-section">
          <h2 class="section-title">最近添加的商品</h2>
          
          <div v-if="goodsStore.loading" class="loading">加载中...</div>
          
          <div v-else-if="recentGoods.length" class="recent-goods">
            <div 
              v-for="goods in recentGoods" 
              :key="goods.id"
              class="goods-item"
            >
              <img :src="goods.picture" :alt="goods.name" class="goods-image">
              <div class="goods-info">
                <h4 class="goods-name">{{ goods.name }}</h4>
                <p class="goods-price">¥{{ goods.price }}</p>
                <p class="goods-stock">库存: {{ goods.stock }}</p>
              </div>
            </div>
          </div>
          
          <div v-else class="empty">暂无商品</div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useGoodsStore } from '../stores/goods'

const router = useRouter()
const authStore = useAuthStore()
const goodsStore = useGoodsStore()

// 计算属性
const totalStock = computed(() => {
  if (!Array.isArray(goodsStore.goodsList)) return 0
  return goodsStore.goodsList.reduce((total, goods) => total + (goods.stock || 0), 0)
})

const totalValue = computed(() => {
  if (!Array.isArray(goodsStore.goodsList)) return 0
  return goodsStore.goodsList.reduce((total, goods) => {
    return total + ((goods.price || 0) * (goods.stock || 0))
  }, 0)
})

const recentGoods = computed(() => {
  if (!Array.isArray(goodsStore.goodsList)) return []
  return goodsStore.goodsList.slice(0, 6)
})

onMounted(async () => {
  // 初始化认证状态
  await authStore.initAuth()
  
  // 获取数据
  await goodsStore.fetchGoodsList()
  await goodsStore.fetchCategories()
})

const logout = () => {
  if (confirm('确定要退出登录吗？')) {
    authStore.logout()
    router.push('/login')
  }
}
</script>

<style scoped>
.dashboard {
  display: flex;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.sidebar {
  width: 250px;
  background: #2c3e50;
  color: white;
  flex-shrink: 0;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #34495e;
}

.logo {
  font-size: 1.3rem;
  font-weight: bold;
  margin: 0;
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.3s ease;
}

.nav-item:hover,
.nav-item.active {
  background: #34495e;
  color: white;
}

.nav-icon {
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.nav-text {
  font-size: 0.95rem;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.top-bar {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.admin-name {
  font-weight: 500;
  color: #2c3e50;
}

.logout-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.logout-btn:hover {
  background: #c0392b;
}

.content {
  flex: 1;
  padding: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 2.5rem;
  background: #3498db;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.stat-label {
  color: #7f8c8d;
  margin: 0;
  font-size: 0.9rem;
}

.recent-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.loading,
.empty {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
}

.recent-goods {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.goods-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #ecf0f1;
  border-radius: 6px;
  transition: box-shadow 0.2s;
}

.goods-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.goods-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 1rem;
  font-weight: 500;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.goods-price {
  color: #e74c3c;
  font-weight: bold;
  margin: 0 0 0.25rem 0;
}

.goods-stock {
  color: #27ae60;
  font-size: 0.9rem;
  margin: 0;
}

@media (max-width: 768px) {
  .dashboard {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    order: 2;
  }
  
  .main-content {
    order: 1;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
