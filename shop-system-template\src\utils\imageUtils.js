/**
 * 图片处理工具函数
 */

// 后端服务器地址
const SERVER_BASE_URL = 'http://127.0.0.1:8360'

/**
 * 检查图片是否有效
 * @param {string} picture - 图片路径或URL
 * @returns {boolean} 是否有效
 */
export const hasValidPicture = (picture) => {
  if (!picture) return false
  
  // 如果是在线URL（http/https开头），直接返回true
  if (picture.startsWith('http://') || picture.startsWith('https://')) {
    return true
  }
  
  // 如果是本地路径，检查是否是有效路径
  if (picture.startsWith('static/')) {
    return true
  }
  
  // 其他情况检查URL格式
  try {
    const url = new URL(picture)
    return url.pathname !== '/' && url.pathname !== ''
  } catch {
    return false
  }
}

/**
 * 获取完整的图片URL
 * @param {string} picture - 图片路径或URL
 * @param {string} defaultImage - 默认图片路径
 * @returns {string} 完整的图片URL
 */
export const getImageUrl = (picture, defaultImage = '/default-category.png') => {
  if (!picture) return defaultImage
  
  // 如果是在线URL，直接返回
  if (picture.startsWith('http://') || picture.startsWith('https://')) {
    return picture
  }
  
  // 如果是本地路径，拼接后端服务器地址
  if (picture.startsWith('static/')) {
    return `${SERVER_BASE_URL}/${picture}`
  }
  
  // 其他情况直接返回
  return picture
}

/**
 * 判断是否为在线图片URL
 * @param {string} picture - 图片路径或URL
 * @returns {boolean} 是否为在线URL
 */
export const isOnlineUrl = (picture) => {
  if (!picture) return false
  return picture.startsWith('http://') || picture.startsWith('https://')
}

/**
 * 判断是否为本地图片路径
 * @param {string} picture - 图片路径或URL
 * @returns {boolean} 是否为本地路径
 */
export const isLocalPath = (picture) => {
  if (!picture) return false
  return picture.startsWith('static/')
}

/**
 * 验证图片URL格式
 * @param {string} url - 图片URL
 * @returns {boolean} 是否为有效的URL格式
 */
export const isValidImageUrl = (url) => {
  if (!url) return false
  
  try {
    const urlObj = new URL(url)
    // 检查协议
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false
    }
    
    // 检查是否有路径
    if (urlObj.pathname === '/') {
      return false
    }
    
    return true
  } catch {
    return false
  }
}

/**
 * 检查文件是否为图片类型
 * @param {File} file - 文件对象
 * @returns {boolean} 是否为图片文件
 */
export const isImageFile = (file) => {
  if (!file) return false
  return file.type.startsWith('image/')
}

/**
 * 检查文件大小是否符合要求
 * @param {File} file - 文件对象
 * @param {number} maxSizeMB - 最大文件大小（MB）
 * @returns {boolean} 文件大小是否符合要求
 */
export const isValidFileSize = (file, maxSizeMB = 2) => {
  if (!file) return false
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  return file.size <= maxSizeBytes
}

/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
