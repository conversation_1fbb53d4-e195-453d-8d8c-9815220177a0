/**
 * 图片处理工具函数
 */

// 后端服务器地址
const SERVER_BASE_URL = 'http://127.0.0.1:8360'

/**
 * 检查图片是否有效
 * @param {string} picture - 图片路径或URL
 * @returns {boolean} 是否有效
 */
export const hasValidPicture = (picture) => {
  if (!picture) return false
  
  // 如果是在线URL（http/https开头），直接返回true
  if (picture.startsWith('http://') || picture.startsWith('https://')) {
    return true
  }
  
  // 如果是本地路径，检查是否是有效路径
  if (picture.startsWith('static/')) {
    return true
  }
  
  // 其他情况检查URL格式
  try {
    const url = new URL(picture)
    return url.pathname !== '/' && url.pathname !== ''
  } catch {
    return false
  }
}

/**
 * 获取完整的图片URL
 * @param {string} picture - 图片路径或URL
 * @param {string} defaultImage - 默认图片路径
 * @returns {string} 完整的图片URL
 */
export const getImageUrl = (picture, defaultImage = '') => {
  if (!picture) return defaultImage
  
  // 如果是在线URL，直接返回
  if (picture.startsWith('http://') || picture.startsWith('https://')) {
    return picture
  }
  
  // 如果是本地路径，拼接后端服务器地址
  if (picture.startsWith('static/')) {
    return `${SERVER_BASE_URL}/${picture}`
  }
  
  // 其他情况直接返回
  return picture
}

/**
 * 判断是否为在线图片URL
 * @param {string} picture - 图片路径或URL
 * @returns {boolean} 是否为在线URL
 */
export const isOnlineUrl = (picture) => {
  if (!picture) return false
  return picture.startsWith('http://') || picture.startsWith('https://')
}

/**
 * 判断是否为本地图片路径
 * @param {string} picture - 图片路径或URL
 * @returns {boolean} 是否为本地路径
 */
export const isLocalPath = (picture) => {
  if (!picture) return false
  return picture.startsWith('static/')
}

/**
 * 验证图片URL格式
 * @param {string} url - 图片URL
 * @returns {boolean} 是否为有效的URL格式
 */
export const isValidImageUrl = (url) => {
  if (!url) return false
  
  try {
    const urlObj = new URL(url)
    // 检查协议
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false
    }
    
    // 检查是否有路径
    if (urlObj.pathname === '/') {
      return false
    }
    
    // 检查文件扩展名（可选）
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
    const pathname = urlObj.pathname.toLowerCase()
    const hasImageExtension = imageExtensions.some(ext => pathname.endsWith(ext))
    
    // 如果没有明确的图片扩展名，仍然认为可能是有效的（某些CDN不显示扩展名）
    return true
  } catch {
    return false
  }
}

/**
 * 获取图片文件扩展名
 * @param {string} picture - 图片路径或URL
 * @returns {string} 文件扩展名（不包含点号）
 */
export const getImageExtension = (picture) => {
  if (!picture) return ''
  
  try {
    const url = new URL(picture)
    const pathname = url.pathname
    const lastDot = pathname.lastIndexOf('.')
    if (lastDot === -1) return ''
    return pathname.substring(lastDot + 1).toLowerCase()
  } catch {
    // 如果不是有效URL，直接从字符串中提取
    const lastDot = picture.lastIndexOf('.')
    if (lastDot === -1) return ''
    return picture.substring(lastDot + 1).toLowerCase()
  }
}

/**
 * 检查文件是否为图片类型
 * @param {File} file - 文件对象
 * @returns {boolean} 是否为图片文件
 */
export const isImageFile = (file) => {
  if (!file) return false
  return file.type.startsWith('image/')
}

/**
 * 检查文件大小是否符合要求
 * @param {File} file - 文件对象
 * @param {number} maxSizeMB - 最大文件大小（MB）
 * @returns {boolean} 文件大小是否符合要求
 */
export const isValidFileSize = (file, maxSizeMB = 2) => {
  if (!file) return false
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  return file.size <= maxSizeBytes
}

/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 创建图片预览URL
 * @param {File} file - 图片文件
 * @returns {Promise<string>} 预览URL
 */
export const createImagePreview = (file) => {
  return new Promise((resolve, reject) => {
    if (!isImageFile(file)) {
      reject(new Error('不是有效的图片文件'))
      return
    }
    
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = () => reject(new Error('读取文件失败'))
    reader.readAsDataURL(file)
  })
}

/**
 * 压缩图片文件
 * @param {File} file - 原始图片文件
 * @param {number} maxWidth - 最大宽度
 * @param {number} maxHeight - 最大高度
 * @param {number} quality - 压缩质量 (0-1)
 * @returns {Promise<Blob>} 压缩后的图片Blob
 */
export const compressImage = (file, maxWidth = 800, maxHeight = 600, quality = 0.8) => {
  return new Promise((resolve, reject) => {
    if (!isImageFile(file)) {
      reject(new Error('不是有效的图片文件'))
      return
    }
    
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      // 计算新的尺寸
      let { width, height } = img
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }
      }
      
      canvas.width = width
      canvas.height = height
      
      // 绘制压缩后的图片
      ctx.drawImage(img, 0, 0, width, height)
      
      // 转换为Blob
      canvas.toBlob(resolve, file.type, quality)
    }
    
    img.onerror = () => reject(new Error('图片加载失败'))
    img.src = URL.createObjectURL(file)
  })
}
