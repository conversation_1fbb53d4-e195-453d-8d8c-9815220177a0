<template>
  <div class="dashboard">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <h2 class="logo">商城管理</h2>
      </div>
      
      <nav class="sidebar-nav">
        <router-link to="/" class="nav-item">
          <span class="nav-icon">📊</span>
          <span class="nav-text">仪表板</span>
        </router-link>
        
        <router-link to="/goods" class="nav-item">
          <span class="nav-icon">📦</span>
          <span class="nav-text">商品管理</span>
        </router-link>
        
        <router-link to="/categories" class="nav-item" active-class="active">
          <span class="nav-icon">📂</span>
          <span class="nav-text">分类管理</span>
        </router-link>
        
        <router-link to="/profile" class="nav-item">
          <span class="nav-icon">👤</span>
          <span class="nav-text">个人资料</span>
        </router-link>
      </nav>
    </aside>
    
    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 顶部栏 -->
      <header class="top-bar">
        <div class="top-bar-left">
          <h1 class="page-title">分类管理</h1>
        </div>
        
        <div class="top-bar-right">
          <button @click="showAddModal = true" class="add-btn">添加分类</button>
          <div class="admin-info">
            <span class="admin-name">{{ authStore.admin?.username || '管理员' }}</span>
            <button @click="logout" class="logout-btn">退出登录</button>
          </div>
        </div>
      </header>
      
      <!-- 内容区域 -->
      <div class="content">
        <div v-if="goodsStore.loading" class="loading">加载中...</div>
        
        <div v-else-if="Array.isArray(goodsStore.categories) && goodsStore.categories.length" class="categories-container">
          <!-- 一级分类 -->
          <div class="category-section">
            <h2 class="section-title">一级分类</h2>
            <div class="categories-grid">
              <div
                v-for="category in parentCategories"
                :key="category.id"
                class="category-card parent-category"
              >
                <img :src="getImageUrl(category.picture)" :alt="category.name" class="category-image">
                <div class="category-info">
                  <h3 class="category-name">{{ category.name }}</h3>
                  <p class="category-meta">
                    <span class="category-id">ID: {{ category.id }}</span>
                    <span class="sub-count">子分类: {{ getSubCategoriesCount(category.id) }}</span>
                  </p>
                </div>
                <div class="category-actions">
                  <button @click="editCategory(category)" class="edit-btn">编辑</button>
                  <button @click="deleteCategory(category.id)" class="delete-btn">删除</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 二级分类 -->
          <div class="category-section">
            <h2 class="section-title">二级分类</h2>
            <div class="categories-grid">
              <div
                v-for="category in subCategories"
                :key="category.id"
                class="category-card sub-category"
              >
                <img :src="getImageUrl(category.picture)" :alt="category.name" class="category-image">
                <div class="category-info">
                  <h3 class="category-name">{{ category.name }}</h3>
                  <p class="category-meta">
                    <span class="category-id">ID: {{ category.id }}</span>
                    <span class="parent-name">上级: {{ getParentCategoryName(category.pid) }}</span>
                  </p>
                </div>
                <div class="category-actions">
                  <button @click="editCategory(category)" class="edit-btn">编辑</button>
                  <button @click="deleteCategory(category.id)" class="delete-btn">删除</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="empty">暂无分类</div>
      </div>
    </main>
    
    <!-- 添加/编辑分类模态框 -->
    <div v-if="showAddModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>{{ showAddModal ? '添加分类' : '编辑分类' }}</h3>
          <button @click="closeModal" class="close-btn">×</button>
        </div>
        
        <form @submit.prevent="submitForm" class="modal-form">
          <div class="form-group">
            <label>分类名称</label>
            <input v-model="form.name" type="text" required placeholder="请输入分类名称">
          </div>
          
          <div class="form-group">
            <label>分类图片</label>
            <div class="image-upload-section">
              <!-- 图片上传方式选择 -->
              <div class="upload-type-selector">
                <label class="radio-option">
                  <input type="radio" v-model="uploadType" value="url" name="uploadType">
                  <span>在线图片URL</span>
                </label>
                <label class="radio-option">
                  <input type="radio" v-model="uploadType" value="file" name="uploadType">
                  <span>本地文件上传</span>
                </label>
              </div>

              <!-- URL输入 -->
              <div v-if="uploadType === 'url'" class="url-input-section">
                <input
                  v-model="form.picture"
                  type="url"
                  placeholder="请输入图片URL（如：https://example.com/image.jpg）"
                  class="url-input"
                >
                <small class="form-help">支持 http:// 或 https:// 开头的图片链接</small>
              </div>

              <!-- 文件上传 -->
              <div v-if="uploadType === 'file'" class="file-upload-section">
                <input
                  type="file"
                  @change="handleFileChange"
                  accept="image/*"
                  class="file-input"
                  ref="fileInput"
                >
                <div v-if="uploading" class="upload-status">
                  <div class="upload-progress">
                    <div class="progress-bar"></div>
                  </div>
                  <span class="upload-text">正在上传图片...</span>
                </div>
                <small class="form-help">支持 JPG、PNG、GIF 格式，文件大小不超过 2MB，选择文件后自动上传</small>
              </div>

              <!-- 图片预览 -->
              <div v-if="form.picture" class="image-preview">
                <img :src="getImageUrl(form.picture)" :alt="form.name" class="preview-image">
                <button type="button" @click="clearImage" class="clear-btn">清除图片</button>
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label>上级分类</label>
            <select v-model="form.pid">
              <option value="0">无上级分类（创建一级分类）</option>
              <option v-for="category in parentCategories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
            <small class="form-help">选择"无上级分类"创建一级分类，选择已有分类创建二级分类</small>
          </div>
          
          <div class="form-actions">
            <button type="button" @click="closeModal" class="cancel-btn">取消</button>
            <button type="submit" :disabled="submitting" class="submit-btn">
              {{ submitting ? '提交中...' : '确定' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useGoodsStore } from '../stores/goods'
import { getImageUrl, isImageFile, isValidFileSize } from '../utils/imageUtils'

const router = useRouter()
const authStore = useAuthStore()
const goodsStore = useGoodsStore()

const showAddModal = ref(false)
const showEditModal = ref(false)
const submitting = ref(false)
const uploadType = ref('url') // 'url' 或 'file'
const selectedFile = ref(null)
const uploading = ref(false)
const fileInput = ref(null)

const form = ref({
  id: null,
  name: '',
  picture: '',
  pid: 0
})

// 计算属性：一级分类（pid = 0）
const parentCategories = computed(() => {
  return goodsStore.categories.filter(cat => cat.pid === 0)
})

// 计算属性：二级分类（pid > 0）
const subCategories = computed(() => {
  return goodsStore.categories.filter(cat => cat.pid > 0)
})

// 获取指定一级分类下的子分类数量
const getSubCategoriesCount = (parentId) => {
  return goodsStore.categories.filter(cat => cat.pid === parentId).length
}

// 获取父级分类名称
const getParentCategoryName = (parentId) => {
  const parent = goodsStore.categories.find(cat => cat.id === parentId)
  return parent ? parent.name : '未知'
}

// 图片处理函数已移至 utils/imageUtils.js

// 处理文件选择并自动上传
const handleFileChange = async (event) => {
  const file = event.target.files[0]
  if (file) {
    // 检查文件类型
    if (!isImageFile(file)) {
      alert('请选择图片文件')
      // 清空文件选择
      if (fileInput.value) {
        fileInput.value.value = ''
      }
      return
    }

    // 检查文件大小（2MB）
    if (!isValidFileSize(file, 2)) {
      alert('文件大小不能超过 2MB')
      // 清空文件选择
      if (fileInput.value) {
        fileInput.value.value = ''
      }
      return
    }

    selectedFile.value = file

    // 自动上传文件
    await uploadFile()
  }
}

// 上传文件（自动调用）
const uploadFile = async () => {
  if (!selectedFile.value) {
    return
  }

  uploading.value = true

  try {
    const result = await goodsStore.uploadFile(selectedFile.value, 'category_picture')

    if (result.success) {
      // 上传成功，设置图片路径
      // 后端返回 { url, savepath }，优先使用 savepath（相对路径）
      form.value.picture = result.data.savepath || result.data.url

      // 显示成功提示（不用弹窗，用户体验更好）
      console.log('图片上传成功:', form.value.picture)

      // 清除文件选择状态
      selectedFile.value = null
    } else {
      alert(result.message || '图片上传失败')
      // 上传失败时清空文件选择
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }
  } catch (error) {
    alert(error.message || '图片上传失败')
    // 上传失败时清空文件选择
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  } finally {
    uploading.value = false
  }
}

// 清除图片
const clearImage = () => {
  form.value.picture = ''
  selectedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

onMounted(async () => {
  await authStore.initAuth()
  await goodsStore.fetchCategories()
})

const editCategory = (category) => {
  form.value = { ...category }
  showEditModal.value = true
}

const deleteCategory = async (id) => {
  if (confirm('确定要删除这个分类吗？')) {
    const result = await goodsStore.deleteCategory(id)
    if (result.success) {
      alert('删除成功')
    } else {
      alert(result.message || '删除失败')
    }
  }
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  resetForm()
}

const resetForm = () => {
  form.value = {
    id: null,
    name: '',
    picture: '',
    pid: 0
  }
  uploadType.value = 'url'
  selectedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const submitForm = async () => {
  submitting.value = true
  
  try {
    let result
    if (showAddModal.value) {
      result = await goodsStore.addCategory(form.value)
    } else {
      result = await goodsStore.updateCategory(form.value)
    }
    
    if (result.success) {
      alert(showAddModal.value ? '添加成功' : '更新成功')
      closeModal()
    } else {
      alert(result.message || '操作失败')
    }
  } catch (error) {
    alert(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const logout = () => {
  if (confirm('确定要退出登录吗？')) {
    authStore.logout()
    router.push('/login')
  }
}
</script>

<style scoped>
/* 复用之前的样式 */
.dashboard {
  display: flex;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.sidebar {
  width: 250px;
  background: #2c3e50;
  color: white;
  flex-shrink: 0;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #34495e;
}

.logo {
  font-size: 1.3rem;
  font-weight: bold;
  margin: 0;
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.3s ease;
}

.nav-item:hover,
.nav-item.active {
  background: #34495e;
  color: white;
}

.nav-icon {
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.nav-text {
  font-size: 0.95rem;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.top-bar {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top-bar-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0;
}

.add-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.add-btn:hover {
  background: #219a52;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.admin-name {
  font-weight: 500;
  color: #2c3e50;
}

.logout-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.logout-btn:hover {
  background: #c0392b;
}

.content {
  flex: 1;
  padding: 2rem;
}

.loading,
.empty {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
  background: white;
  border-radius: 8px;
}

.categories-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.category-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #3498db;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.category-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.category-card.parent-category {
  border-color: #3498db;
  background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
}

.category-card.sub-category {
  border-color: #27ae60;
  background: linear-gradient(135deg, #e8f5e9 0%, #f8f9fa 100%);
}

.category-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 50%;
  margin-bottom: 1rem;
}

.category-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.category-meta {
  color: #7f8c8d;
  font-size: 0.85rem;
  margin: 0 0 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.category-id,
.sub-count,
.parent-name {
  display: block;
}

.sub-count {
  color: #3498db;
  font-weight: 500;
}

.parent-name {
  color: #27ae60;
  font-weight: 500;
}

.category-actions {
  display: flex;
  gap: 0.5rem;
}

.edit-btn,
.delete-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.edit-btn {
  background: #3498db;
  color: white;
}

.edit-btn:hover {
  background: #2980b9;
}

.delete-btn {
  background: #e74c3c;
  color: white;
}

.delete-btn:hover {
  background: #c0392b;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #ecf0f1;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.close-btn:hover {
  color: #2c3e50;
}

.modal-form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-help {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: #6c757d;
  font-style: italic;
}

/* 图片上传相关样式 */
.image-upload-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.upload-type-selector {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
}

.radio-option input[type="radio"] {
  width: auto;
  margin: 0;
}

.url-input-section,
.file-upload-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.url-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.file-upload-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.file-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.upload-btn {
  align-self: flex-start;
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.upload-btn:hover:not(:disabled) {
  background: #0056b3;
}

.upload-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f8f9fa;
}

.preview-image {
  max-width: 150px;
  max-height: 150px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.clear-btn {
  padding: 0.25rem 0.5rem;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.clear-btn:hover {
  background: #c82333;
}

/* 上传状态样式 */
.upload-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.upload-progress {
  width: 100%;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 2px;
  animation: progress-animation 1.5s ease-in-out infinite;
}

@keyframes progress-animation {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

.upload-text {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.cancel-btn,
.submit-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.submit-btn {
  background: #007bff;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #0056b3;
}

.submit-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}
</style>
