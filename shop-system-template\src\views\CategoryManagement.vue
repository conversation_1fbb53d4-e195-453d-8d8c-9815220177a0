<template>
  <div class="dashboard">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <h2 class="logo">商城管理</h2>
      </div>
      
      <nav class="sidebar-nav">
        <router-link to="/" class="nav-item">
          <span class="nav-icon">📊</span>
          <span class="nav-text">仪表板</span>
        </router-link>
        
        <router-link to="/goods" class="nav-item">
          <span class="nav-icon">📦</span>
          <span class="nav-text">商品管理</span>
        </router-link>
        
        <router-link to="/categories" class="nav-item" active-class="active">
          <span class="nav-icon">📂</span>
          <span class="nav-text">分类管理</span>
        </router-link>
        
        <router-link to="/profile" class="nav-item">
          <span class="nav-icon">👤</span>
          <span class="nav-text">个人资料</span>
        </router-link>
      </nav>
    </aside>
    
    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 顶部栏 -->
      <header class="top-bar">
        <div class="top-bar-left">
          <h1 class="page-title">分类管理</h1>
        </div>
        
        <div class="top-bar-right">
          <button @click="showAddModal = true" class="add-btn">添加分类</button>
          <div class="admin-info">
            <span class="admin-name">{{ authStore.admin?.username || '管理员' }}</span>
            <button @click="logout" class="logout-btn">退出登录</button>
          </div>
        </div>
      </header>
      
      <!-- 内容区域 -->
      <div class="content">
        <div v-if="goodsStore.loading" class="loading">加载中...</div>
        
        <div v-else-if="Array.isArray(goodsStore.categories) && goodsStore.categories.length" class="categories-grid">
          <div 
            v-for="category in goodsStore.categories" 
            :key="category.id"
            class="category-card"
          >
            <img :src="category.picture" :alt="category.name" class="category-image">
            <div class="category-info">
              <h3 class="category-name">{{ category.name }}</h3>
              <p class="category-id">ID: {{ category.id }}</p>
            </div>
            <div class="category-actions">
              <button @click="editCategory(category)" class="edit-btn">编辑</button>
              <button @click="deleteCategory(category.id)" class="delete-btn">删除</button>
            </div>
          </div>
        </div>
        
        <div v-else class="empty">暂无分类</div>
      </div>
    </main>
    
    <!-- 添加/编辑分类模态框 -->
    <div v-if="showAddModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>{{ showAddModal ? '添加分类' : '编辑分类' }}</h3>
          <button @click="closeModal" class="close-btn">×</button>
        </div>
        
        <form @submit.prevent="submitForm" class="modal-form">
          <div class="form-group">
            <label>分类名称</label>
            <input v-model="form.name" type="text" required placeholder="请输入分类名称">
          </div>
          
          <div class="form-group">
            <label>分类图片</label>
            <input v-model="form.picture" type="url" placeholder="请输入图片URL">
          </div>
          
          <div class="form-group">
            <label>上级分类</label>
            <select v-model="form.pid">
              <option value="0">无上级分类</option>
              <option v-for="category in goodsStore.categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
          
          <div class="form-actions">
            <button type="button" @click="closeModal" class="cancel-btn">取消</button>
            <button type="submit" :disabled="submitting" class="submit-btn">
              {{ submitting ? '提交中...' : '确定' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useGoodsStore } from '../stores/goods'

const router = useRouter()
const authStore = useAuthStore()
const goodsStore = useGoodsStore()

const showAddModal = ref(false)
const showEditModal = ref(false)
const submitting = ref(false)

const form = ref({
  id: null,
  name: '',
  picture: '',
  pid: 0
})

onMounted(async () => {
  await authStore.initAuth()
  await goodsStore.fetchCategories()
})

const editCategory = (category) => {
  form.value = { ...category }
  showEditModal.value = true
}

const deleteCategory = async (id) => {
  if (confirm('确定要删除这个分类吗？')) {
    const result = await goodsStore.deleteCategory(id)
    if (result.success) {
      alert('删除成功')
    } else {
      alert(result.message || '删除失败')
    }
  }
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  resetForm()
}

const resetForm = () => {
  form.value = {
    id: null,
    name: '',
    picture: '',
    pid: 0
  }
}

const submitForm = async () => {
  submitting.value = true
  
  try {
    let result
    if (showAddModal.value) {
      result = await goodsStore.addCategory(form.value)
    } else {
      result = await goodsStore.updateCategory(form.value)
    }
    
    if (result.success) {
      alert(showAddModal.value ? '添加成功' : '更新成功')
      closeModal()
    } else {
      alert(result.message || '操作失败')
    }
  } catch (error) {
    alert(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const logout = () => {
  if (confirm('确定要退出登录吗？')) {
    authStore.logout()
    router.push('/login')
  }
}
</script>

<style scoped>
/* 复用之前的样式 */
.dashboard {
  display: flex;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.sidebar {
  width: 250px;
  background: #2c3e50;
  color: white;
  flex-shrink: 0;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #34495e;
}

.logo {
  font-size: 1.3rem;
  font-weight: bold;
  margin: 0;
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.3s ease;
}

.nav-item:hover,
.nav-item.active {
  background: #34495e;
  color: white;
}

.nav-icon {
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.nav-text {
  font-size: 0.95rem;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.top-bar {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top-bar-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0;
}

.add-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.add-btn:hover {
  background: #219a52;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.admin-name {
  font-weight: 500;
  color: #2c3e50;
}

.logout-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.logout-btn:hover {
  background: #c0392b;
}

.content {
  flex: 1;
  padding: 2rem;
}

.loading,
.empty {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
  background: white;
  border-radius: 8px;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.category-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.category-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 50%;
  margin-bottom: 1rem;
}

.category-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.category-id {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
}

.category-actions {
  display: flex;
  gap: 0.5rem;
}

.edit-btn,
.delete-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.edit-btn {
  background: #3498db;
  color: white;
}

.edit-btn:hover {
  background: #2980b9;
}

.delete-btn {
  background: #e74c3c;
  color: white;
}

.delete-btn:hover {
  background: #c0392b;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #ecf0f1;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.close-btn:hover {
  color: #2c3e50;
}

.modal-form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.cancel-btn,
.submit-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.submit-btn {
  background: #007bff;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #0056b3;
}

.submit-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}
</style>
