<template>
  <div class="home">
    <!-- 头部导航 -->
    <el-header class="header">
      <div class="header-container">
        <div class="logo-section">
          <h1 class="logo">
            <el-icon class="logo-icon"><Shop /></el-icon>
            我的商城
          </h1>
          <span class="logo-subtitle">精品购物体验</span>
        </div>

        <div class="nav-section">
          <!-- 搜索框 -->
          <div class="search-section">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索商品"
              class="search-input"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
              <template #append>
                <el-button @click="handleSearch">搜索</el-button>
              </template>
            </el-input>
          </div>

          <!-- 购物车按钮 -->
          <el-badge :value="goodsStore.cartCount" :hidden="goodsStore.cartCount === 0" class="cart-badge">
            <el-button type="primary" @click="$router.push('/cart')" class="cart-btn">
              <el-icon><ShoppingCart /></el-icon>
              购物车
            </el-button>
          </el-badge>

          <!-- 用户信息 -->
          <div v-if="authStore.isLoggedIn" class="user-section">
            <el-dropdown @command="handleUserCommand">
              <el-button class="user-btn">
                <el-avatar :size="32" class="user-avatar">
                  {{ authStore.user?.username?.charAt(0)?.toUpperCase() }}
                </el-avatar>
                <span class="user-name">{{ authStore.user?.username }}</span>
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人中心
                  </el-dropdown-item>
                  <el-dropdown-item command="orders">
                    <el-icon><List /></el-icon>
                    我的订单
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <!-- 登录注册 -->
          <div v-else class="auth-section">
            <el-button @click="$router.push('/login')" text>登录</el-button>
            <el-button type="primary" @click="$router.push('/register')">注册</el-button>
          </div>
        </div>
      </div>
    </el-header>

    <!-- 主体布局 -->
    <el-container class="main-container">
      <!-- 左侧分类导航 -->
      <el-aside class="category-sidebar" width="280px">
        <div class="category-header">
          <h3 class="category-title">
            <el-icon><Menu /></el-icon>
            商品分类
          </h3>
        </div>

        <el-scrollbar class="category-scrollbar">
          <div class="category-list">
            <!-- 全部商品 -->
            <div
              class="category-item all-goods"
              :class="{ active: selectedCategory === null }"
              @click="filterByCategory(null)"
            >
              <div class="category-content">
                <el-icon class="category-icon"><Shop /></el-icon>
                <span class="category-name">全部商品</span>
              </div>
              <el-badge
                :value="getTotalGoodsCount()"
                :hidden="getTotalGoodsCount() === 0"
                class="category-badge"
              />
            </div>

            <!-- 一级分类和二级分类 -->
            <div
              v-for="parentCategory in parentCategories"
              :key="parentCategory.id"
              class="parent-category-group"
            >
              <!-- 一级分类标题（只能展开/收起，不能点击查看商品） -->
              <div
                class="parent-category-header"
                :class="{ expanded: expandedCategories.includes(parentCategory.id) }"
                @click="toggleCategory(parentCategory.id)"
              >
                <div class="parent-category-content">
                  <span class="category-emoji">{{ parentCategory.icon }}</span>
                  <span class="parent-category-name">{{ parentCategory.name }}</span>
                  <span class="sub-count">({{ parentCategory.actualSubCategories.length }})</span>
                </div>
                <el-icon class="expand-icon">
                  <ArrowRight v-if="!expandedCategories.includes(parentCategory.id)" />
                  <ArrowDown v-else />
                </el-icon>
              </div>

              <!-- 二级分类列表（只有这些可以点击查看商品） -->
              <el-collapse-transition>
                <div
                  v-show="expandedCategories.includes(parentCategory.id)"
                  class="sub-categories"
                >
                  <div
                    v-for="subCategory in parentCategory.actualSubCategories"
                    :key="subCategory.id"
                    class="category-item sub-category"
                    :class="{ active: selectedCategory === subCategory.id }"
                    @click="filterByCategory(subCategory.id)"
                  >
                    <div class="category-content">
                      <el-image
                        v-if="subCategory.picture"
                        :src="subCategory.picture"
                        class="sub-category-image"
                        fit="cover"
                      >
                        <template #error>
                          <span class="category-emoji">{{ getCategoryIcon(subCategory.name) }}</span>
                        </template>
                      </el-image>
                      <span v-else class="category-emoji">{{ getCategoryIcon(subCategory.name) }}</span>
                      <span class="category-name">{{ subCategory.name }}</span>
                    </div>
                    <el-badge
                      :value="subCategory.goods_count || 0"
                      :hidden="!subCategory.goods_count"
                      class="category-badge"
                    />
                  </div>
                </div>
              </el-collapse-transition>
            </div>
          </div>
        </el-scrollbar>
      </el-aside>

      <!-- 右侧商品列表 -->
      <el-main class="goods-main">
        <!-- 商品区域头部 -->
        <div class="goods-header">
          <div class="goods-title-section">
            <h2 class="goods-title">
              {{ selectedCategory ? getCategoryName(selectedCategory) : '全部商品' }}
            </h2>
            <span class="goods-count">共 {{ goodsStore.goodsList.length }} 件商品</span>
          </div>

          <!-- 排序和筛选 -->
          <div class="goods-controls">
            <el-select v-model="sortBy" placeholder="排序方式" style="width: 120px" @change="handleSort">
              <el-option label="默认" value="default" />
              <el-option label="价格升序" value="price_asc" />
              <el-option label="价格降序" value="price_desc" />
              <el-option label="最新" value="newest" />
            </el-select>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="goodsStore.loading" class="loading-container">
          <el-skeleton :rows="6" animated />
        </div>

        <!-- 商品网格 -->
        <el-scrollbar v-else-if="goodsStore.goodsList.length" class="goods-scrollbar">
          <div class="goods-grid">
            <el-card
              v-for="goods in goodsStore.goodsList"
              :key="goods.id"
              class="goods-card"
              shadow="hover"
              @click="goToDetail(goods.id)"
            >
              <template #header>
                <div class="goods-image-container">
                  <el-image
                    :src="goods.picture"
                    :alt="goods.name"
                    class="goods-image"
                    fit="cover"
                    :preview-src-list="[goods.picture]"
                    preview-teleported
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>

                  <!-- 商品标签 -->
                  <div class="goods-tags">
                    <el-tag v-if="goods.is_new" type="success" size="small">新品</el-tag>
                    <el-tag v-if="goods.is_hot" type="danger" size="small">热销</el-tag>
                  </div>
                </div>
              </template>

              <div class="goods-content">
                <h3 class="goods-name" :title="goods.name">{{ goods.name }}</h3>

                <div class="goods-price-section">
                  <span class="goods-price">¥{{ goods.price }}</span>
                  <span class="goods-spec" v-if="goods.spec">{{ goods.spec }}</span>
                </div>

                <p class="goods-description" :title="goods.description">
                  {{ truncateDescription(goods.description) }}
                </p>

                <div class="goods-footer">
                  <div class="goods-stock">
                    <el-icon><Box /></el-icon>
                    库存: {{ goods.stock || 0 }}
                  </div>

                  <el-button
                    type="primary"
                    size="small"
                    @click.stop="addToCart(goods)"
                    :disabled="!goods.stock"
                  >
                    <el-icon><ShoppingCart /></el-icon>
                    加入购物车
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>
        </el-scrollbar>

        <!-- 空状态 -->
        <el-empty v-else description="暂无商品" class="empty-state">
          <el-button type="primary" @click="filterByCategory(null)">查看全部商品</el-button>
        </el-empty>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../stores/auth'
import { useGoodsStore } from '../stores/goods'

const router = useRouter()
const authStore = useAuthStore()
const goodsStore = useGoodsStore()

const selectedCategory = ref(null)
const sortBy = ref('default')
const searchKeyword = ref('')
const expandedCategories = ref([]) // 展开的一级分类

// 前端自定义的一级分类分组配置
const customCategoryGroups = [
  {
    id: 'fashion',
    name: '潮流女装',
    icon: '👗',
    keywords: ['服装', '女装', '衣服', '裙', '外套', '大衣', '羽绒'], // 关键词匹配
    subCategories: ['羽绒服', '毛呢大衣', '连衣裙'] // 已知分类
  },
  {
    id: 'food',
    name: '食品',
    icon: '🍎',
    keywords: ['食品', '零食', '果蔬', '饮料', '茶', '粮油', '调味'],
    subCategories: ['休闲零食', '生鲜果蔬', '饮料汽水', '四季茗茶', '粮油调味']
  },
  {
    id: 'jewelry',
    name: '珠宝配饰',
    icon: '💎',
    keywords: ['饰品', '手表', '珠宝', '配饰', 'DIY'],
    subCategories: ['时尚饰品', '品质手表', 'DIY饰品']
  },
  {
    id: 'daily',
    name: '日用百货',
    icon: '🧴',
    keywords: ['日用', '清洁', '居家', '碗', '杯', '收纳'],
    subCategories: ['居家日用', '个人清洁', '盆碗碟筷', '茶杯茶具', '收纳整理']
  },
  {
    id: 'digital',
    name: '手机数码',
    icon: '📱',
    keywords: ['手机', '数码', '电脑', '笔记本', '平板', '相机', '配件'],
    subCategories: ['手机', '笔记本', '数码配件', '平板', '相机']
  },
  {
    id: 'sports',
    name: '户外运动',
    icon: '⚽',
    keywords: ['运动', '户外', '球类', '垂钓', '骑行', '电动车'],
    subCategories: ['运动鞋', '球类运动', '垂钓用品', '运动服', '骑行装备', '电动车']
  },
  {
    id: 'other',
    name: '其他分类',
    icon: '🏷️',
    keywords: [], // 其他分类作为兜底
    subCategories: []
  }
]

// 智能分类归属函数
const getCategoryGroup = (categoryName) => {
  // 1. 首先检查是否在已知分类中
  for (const group of customCategoryGroups) {
    if (group.subCategories.includes(categoryName)) {
      return group.id
    }
  }

  // 2. 通过关键词匹配
  for (const group of customCategoryGroups) {
    if (group.keywords.some(keyword => categoryName.includes(keyword))) {
      return group.id
    }
  }

  // 3. 默认归类到"其他分类"
  return 'other'
}

// 计算属性：动态生成分类组
const parentCategories = computed(() => {
  const groups = customCategoryGroups.map(group => ({
    ...group,
    actualSubCategories: goodsStore.categories.filter(cat => {
      // 检查分类是否属于该组
      const belongsToGroup = getCategoryGroup(cat.name) === group.id
      return belongsToGroup
    })
  })).filter(group => group.actualSubCategories.length > 0) // 只显示有分类的组

  return groups
})

// 注意：getSubCategories 函数已移除，现在直接使用 parentCategory.actualSubCategories

// 切换分类展开/收起
const toggleCategory = (groupId) => {
  const index = expandedCategories.value.indexOf(groupId)
  if (index > -1) {
    expandedCategories.value.splice(index, 1)
  } else {
    expandedCategories.value.push(groupId)
  }
}

onMounted(async () => {
  // 初始化认证状态
  await authStore.initAuth()

  // 获取分类和商品列表
  await goodsStore.fetchCategories()
  await goodsStore.fetchGoodsList()

  // 默认展开第一个分类组
  if (parentCategories.value.length > 0) {
    expandedCategories.value.push(parentCategories.value[0].id)
  }
})

const filterByCategory = async (categoryId) => {
  selectedCategory.value = categoryId
  const params = categoryId ? { category_id: categoryId } : {}
  await goodsStore.fetchGoodsList(params)
}

const goToDetail = (goodsId) => {
  router.push(`/goods/${goodsId}`)
}

const addToCart = (goods) => {
  goodsStore.addToCart(goods)
  // 使用更优雅的提示方式
  showToast('已添加到购物车 🛒')
}

// 获取分类图标
const getCategoryIcon = (categoryName) => {
  const iconMap = {
    // 一级分类
    '潮流女装': '👗',
    '食品': '🍎',
    '珠宝配饰': '💎',
    '日用百货': '🧴',
    '手机数码': '📱',
    '户外运动': '⚽',

    // 二级分类 - 潮流女装
    '羽绒服': '🧥',
    '毛呢大衣': '🧥',
    '连衣裙': '👗',

    // 二级分类 - 食品
    '休闲零食': '🍿',
    '生鲜果蔬': '🥬',
    '饮料汽水': '🥤',
    '四季茗茶': '🍵',
    '粮油调味': '🛢️',

    // 二级分类 - 珠宝配饰
    '时尚饰品': '💍',
    '品质手表': '⌚',
    'DIY饰品': '🎨',

    // 二级分类 - 日用百货
    '居家日用': '🏠',
    '个人清洁': '🧼',
    '盆碗碟筷': '🍽️',
    '茶杯茶具': '☕',
    '收纳整理': '📦',

    // 二级分类 - 手机数码
    '手机': '📱',
    '笔记本': '💻',
    '数码配件': '🎧',
    '平板': '📱',
    '相机': '📷',

    // 二级分类 - 户外运动
    '运动鞋': '👟',
    '球类运动': '⚽',
    '垂钓用品': '🎣',
    '运动服': '👕',
    '骑行装备': '🚴',
    '电动车': '🛵'
  }
  return iconMap[categoryName] || '🏷️'
}

// 截断描述文本
const truncateDescription = (description) => {
  if (!description) return ''
  return description.length > 60 ? description.substring(0, 60) + '...' : description
}

// 获取分类名称
const getCategoryName = (categoryId) => {
  const category = goodsStore.categories.find(cat => cat.id === categoryId)
  return category ? category.name : '未知分类'
}

// 获取总商品数量（只计算二级分类的商品数量）
const getTotalGoodsCount = () => {
  return goodsStore.categories
    .filter(cat => cat.parent_id && cat.parent_id > 0) // 只计算二级分类
    .reduce((total, cat) => total + (cat.goods_count || 0), 0)
}

// 处理用户下拉菜单命令
const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'orders':
      router.push('/orders')
      break
    case 'logout':
      authStore.logout()
      ElMessage.success('已退出登录')
      break
  }
}

// 处理排序
const handleSort = (value) => {
  // 这里可以实现排序逻辑
  console.log('排序方式:', value)
}

// 处理搜索
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  router.push({ name: 'Search', query: { q: searchKeyword.value } })
}

// 使用 Element Plus 的消息提示
const showToast = (message) => {
  ElMessage({
    message,
    type: 'success',
    duration: 3000,
    showClose: true
  })
}
</script>

<style scoped>
.home {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

/* 头部样式 */
.header {
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 70px !important;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.logo {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  font-size: 28px;
  color: #409eff;
}

.logo-subtitle {
  font-size: 12px;
  color: #909399;
}

.nav-section {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
  justify-content: flex-end;
}

.search-section {
  flex: 1;
  max-width: 400px;
  margin-right: 20px;
}

.search-input {
  width: 100%;
}

.cart-btn {
  display: flex;
  align-items: center;
  gap: 6px;
}

.user-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  background: transparent;
}

.user-name {
  font-weight: 500;
  color: #606266;
}

.auth-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 主容器 */
.main-container {
  flex: 1;
  height: calc(100vh - 70px);
}

/* 左侧分类栏 */
.category-sidebar {
  background: white;
  border-right: 1px solid #e4e7ed;
  height: 100%;
  overflow: hidden;
}

.category-header {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.category-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-scrollbar {
  height: calc(100% - 80px);
}

.category-list {
  padding: 12px;
}

/* 全部商品项 */
.category-item.all-goods {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
  background: #f8f9fa;
}

.category-item.all-goods:hover {
  background: #e3f2fd;
  border-color: #2196f3;
}

.category-item.all-goods.active {
  background: #2196f3;
  color: white;
  border-color: #2196f3;
}

/* 一级分类组 */
.parent-category-group {
  margin-bottom: 12px;
}

/* 一级分类标题 */
.parent-category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f5f5f5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #e0e0e0;
  margin-bottom: 4px;
}

.parent-category-header:hover {
  background: #eeeeee;
  border-color: #bdbdbd;
}

.parent-category-header.expanded {
  background: #e8f5e8;
  border-color: #4caf50;
}

.parent-category-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.parent-category-name {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.sub-count {
  font-size: 12px;
  color: #999;
  font-weight: 400;
  margin-left: 4px;
}

.sub-count {
  font-size: 12px;
  color: #999;
  font-weight: 400;
  margin-left: 4px;
}

.expand-icon {
  font-size: 16px;
  color: #666;
  transition: transform 0.3s;
}

.parent-category-header.expanded .expand-icon {
  transform: rotate(90deg);
}

/* 二级分类容器 */
.sub-categories {
  padding-left: 16px;
  margin-top: 4px;
}

/* 二级分类项 */
.category-item.sub-category {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
  background: white;
}

.category-item.sub-category:hover {
  background: #f0f9ff;
  border-color: #409eff;
  transform: translateX(4px);
}

.category-item.sub-category.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.category-content {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.category-icon {
  font-size: 18px;
  color: #409eff;
}

.category-item.active .category-icon {
  color: white;
}

.category-emoji {
  font-size: 18px;
}

.sub-category-image {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  object-fit: cover;
}

.category-name {
  font-size: 13px;
  font-weight: 500;
}

.parent-category-name {
  font-size: 14px;
  font-weight: 600;
}

.category-badge {
  margin-left: 8px;
}

/* 右侧商品区域 */
.goods-main {
  background: #f5f7fa;
  padding: 0;
  height: 100%;
  overflow: hidden;
}

.goods-header {
  background: white;
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
}

.goods-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.goods-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.goods-count {
  font-size: 14px;
  color: #909399;
}

.goods-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 加载状态 */
.loading-container {
  padding: 40px;
}

/* 商品滚动区域 */
.goods-scrollbar {
  height: calc(100vh - 70px - 80px);
  padding: 24px;
}

.goods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

/* 商品卡片 */
.goods-card {
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 12px;
  overflow: hidden;
}

.goods-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.goods-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.goods-image {
  width: 100%;
  height: 100%;
  border-radius: 0;
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f5f7fa;
  color: #c0c4cc;
  font-size: 24px;
}

.goods-tags {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  gap: 4px;
}

.goods-content {
  padding: 16px;
}

.goods-name {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.goods-price {
  font-size: 20px;
  font-weight: 700;
  color: #f56c6c;
}

.goods-spec {
  background: #f0f2f5;
  color: #606266;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.goods-description {
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-stock {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

/* 空状态 */
.empty-state {
  height: calc(100vh - 70px - 80px);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .goods-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
  }

  .category-sidebar {
    width: 100% !important;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
  }

  .category-scrollbar {
    height: 300px;
  }

  .category-list {
    padding: 8px;
  }

  .parent-category-header {
    padding: 10px 12px;
    font-size: 14px;
  }

  .parent-category-name {
    font-size: 14px;
  }

  .category-item.sub-category {
    padding: 8px 10px;
  }

  .category-name {
    font-size: 12px;
  }

  .sub-category-image {
    width: 20px;
    height: 20px;
  }

  .category-emoji {
    font-size: 16px;
  }

  .category-item {
    margin-bottom: 0;
  }

  .goods-main {
    height: auto;
  }

  .goods-scrollbar {
    height: auto;
  }

  .goods-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
  }

  .header-container {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    height: auto;
  }

  .header {
    height: auto !important;
  }

  .main-container {
    height: calc(100vh - 120px);
  }
}


</style>
