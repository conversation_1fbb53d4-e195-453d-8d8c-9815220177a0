import { defineStore } from 'pinia'
import request from '../utils/request'

export const useGoodsStore = defineStore('goods', {
  state: () => ({
    goodsList: [],
    categories: [],
    currentGoods: null,
    loading: false
  }),

  actions: {
    // 获取商品列表
    async fetchGoodsList() {
      try {
        this.loading = true
        const response = await request.get('/admin/goods/list')
        
        if (response.errno === 0) {
          this.goodsList = response.data
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('获取商品列表失败:', error)
        return { success: false, message: error.message || '获取商品列表失败' }
      } finally {
        this.loading = false
      }
    },

    // 获取单个商品
    async fetchGoods(id) {
      try {
        this.loading = true
        const response = await request.get('/admin/goods', { params: { id } })
        
        if (response.errno === 0) {
          this.currentGoods = response.data
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('获取商品失败:', error)
        return { success: false, message: error.message || '获取商品失败' }
      } finally {
        this.loading = false
      }
    },

    // 添加商品
    async addGoods(goodsData) {
      try {
        const response = await request.post('/admin/goods/add', goodsData)
        
        if (response.errno === 0) {
          // 重新获取商品列表
          await this.fetchGoodsList()
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('添加商品失败:', error)
        return { success: false, message: error.message || '添加商品失败' }
      }
    },

    // 修改商品
    async updateGoods(goodsData) {
      try {
        const response = await request.post('/admin/goods/save', goodsData)
        
        if (response.errno === 0) {
          // 重新获取商品列表
          await this.fetchGoodsList()
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('修改商品失败:', error)
        return { success: false, message: error.message || '修改商品失败' }
      }
    },

    // 删除商品
    async deleteGoods(id) {
      try {
        const response = await request.post('/admin/goods/del', { id })
        
        if (response.errno === 0) {
          // 重新获取商品列表
          await this.fetchGoodsList()
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('删除商品失败:', error)
        return { success: false, message: error.message || '删除商品失败' }
      }
    },

    // 获取分类列表
    async fetchCategories() {
      try {
        const response = await request.get('/admin/category/list')
        
        if (response.errno === 0) {
          this.categories = response.data
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('获取分类列表失败:', error)
        return { success: false, message: error.message || '获取分类列表失败' }
      }
    },

    // 添加分类
    async addCategory(categoryData) {
      try {
        const response = await request.post('/admin/category/add', categoryData)
        
        if (response.errno === 0) {
          // 重新获取分类列表
          await this.fetchCategories()
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('添加分类失败:', error)
        return { success: false, message: error.message || '添加分类失败' }
      }
    },

    // 修改分类
    async updateCategory(categoryData) {
      try {
        const response = await request.post('/admin/category/save', categoryData)
        
        if (response.errno === 0) {
          // 重新获取分类列表
          await this.fetchCategories()
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('修改分类失败:', error)
        return { success: false, message: error.message || '修改分类失败' }
      }
    },

    // 删除分类
    async deleteCategory(id) {
      try {
        const response = await request.post('/admin/category/del', { id })
        
        if (response.errno === 0) {
          // 重新获取分类列表
          await this.fetchCategories()
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('删除分类失败:', error)
        return { success: false, message: error.message || '删除分类失败' }
      }
    },

    // 文件上传
    async uploadFile(file, type) {
      try {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('type', type)
        
        const response = await request.post('/admin/upload/picture', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        
        if (response.errno === 0) {
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('文件上传失败:', error)
        return { success: false, message: error.message || '文件上传失败' }
      }
    }
  }
})
