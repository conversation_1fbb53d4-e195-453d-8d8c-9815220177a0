import { defineStore } from 'pinia'
import request from '../utils/request'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('admin_token') || '',
    admin: null,
    isAuthenticated: false,
    isInitialized: false // 标记是否已初始化
  }),

  getters: {
    isLoggedIn: (state) => !!state.token && state.isAuthenticated,
    needsInitialization: (state) => !!state.token && !state.isInitialized
  },

  actions: {
    // 管理员登录
    async login(credentials) {
      try {
        const response = await request.post('/admin/login', credentials)
        
        if (response.errno === 0) {
          this.token = response.data.token
          this.isAuthenticated = true
          
          // 存储token到localStorage
          localStorage.setItem('admin_token', this.token)
          
          // 获取管理员信息
          await this.fetchAdmin()
          
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('登录失败:', error)
        return { success: false, message: error.message || '登录失败' }
      }
    },

    // 获取管理员信息
    async fetchAdmin() {
      try {
        const response = await request.get('/admin/admin')

        if (response.errno === 0) {
          this.admin = response.data
          this.isAuthenticated = true
          this.isInitialized = true
          return { success: true, data: response.data }
        } else {
          this.logout() // token无效时清除本地状态
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('获取管理员信息失败:', error)
        this.logout()
        return { success: false, message: error.message || '获取管理员信息失败' }
      }
    },

    // 修改管理员密码
    async changePassword(password) {
      try {
        const response = await request.post('/admin/admin/changePassword', { password })
        
        if (response.errno === 0) {
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        return { success: false, message: error.message || '修改密码失败' }
      }
    },

    // 修改管理员头像
    async changeAvatar(avatar) {
      try {
        const response = await request.post('/admin/admin/changeAvatar', { avatar })
        
        if (response.errno === 0) {
          // 更新本地管理员信息
          if (this.admin) {
            this.admin.avatar = avatar
          }
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('修改头像失败:', error)
        return { success: false, message: error.message || '修改头像失败' }
      }
    },

    // 管理员登出
    logout() {
      this.token = ''
      this.admin = null
      this.isAuthenticated = false
      this.isInitialized = false
      localStorage.removeItem('admin_token')
    },

    // 初始化认证状态
    async initAuth() {
      if (this.token && !this.isInitialized) {
        try {
          await this.fetchAdmin()
        } catch (error) {
          console.error('认证初始化失败:', error)
          this.logout()
          throw error
        }
      }
    },

    // 检查token有效性
    async validateToken() {
      if (!this.token) {
        return false
      }

      try {
        const result = await this.fetchAdmin()
        return result.success
      } catch (error) {
        console.error('Token验证失败:', error)
        this.logout()
        return false
      }
    }
  }
})
