<template>
  <div class="profile-page">
    <el-container>
      <!-- 页面头部 -->
      <el-header class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <el-icon><User /></el-icon>
            个人中心
          </h1>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ name: 'Home' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>个人中心</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="profile-main">
        <el-row :gutter="24">
          <!-- 左侧用户信息 -->
          <el-col :xs="24" :sm="24" :md="8" :lg="6">
            <el-card class="user-card" shadow="hover">
              <div class="user-info">
                <el-avatar :size="80" class="user-avatar">
                  {{ userStore.user?.username?.charAt(0)?.toUpperCase() }}
                </el-avatar>
                <h3 class="username">{{ userStore.user?.username }}</h3>
                <p class="user-email">{{ userStore.user?.email || '未设置邮箱' }}</p>
                <el-button type="primary" @click="editProfileVisible = true">
                  编辑资料
                </el-button>
              </div>
            </el-card>

            <!-- 快捷操作 -->
            <el-card class="quick-actions" shadow="hover">
              <template #header>
                <span>快捷操作</span>
              </template>
              <div class="action-list">
                <div class="action-item" @click="$router.push('/orders')">
                  <el-icon><List /></el-icon>
                  <span>我的订单</span>
                  <el-icon class="arrow"><ArrowRight /></el-icon>
                </div>
                <div class="action-item" @click="$router.push('/cart')">
                  <el-icon><ShoppingCart /></el-icon>
                  <span>购物车</span>
                  <el-icon class="arrow"><ArrowRight /></el-icon>
                </div>
                <div class="action-item" @click="addressVisible = true">
                  <el-icon><Location /></el-icon>
                  <span>收货地址</span>
                  <el-icon class="arrow"><ArrowRight /></el-icon>
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 右侧内容区域 -->
          <el-col :xs="24" :sm="24" :md="16" :lg="18">
            <!-- 统计卡片 -->
            <el-row :gutter="16" class="stats-row">
              <el-col :xs="12" :sm="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.totalOrders }}</div>
                    <div class="stat-label">总订单</div>
                  </div>
                  <el-icon class="stat-icon orders"><List /></el-icon>
                </el-card>
              </el-col>
              <el-col :xs="12" :sm="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.pendingOrders }}</div>
                    <div class="stat-label">待付款</div>
                  </div>
                  <el-icon class="stat-icon pending"><Clock /></el-icon>
                </el-card>
              </el-col>
              <el-col :xs="12" :sm="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.completedOrders }}</div>
                    <div class="stat-label">已完成</div>
                  </div>
                  <el-icon class="stat-icon completed"><Check /></el-icon>
                </el-card>
              </el-col>
              <el-col :xs="12" :sm="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">¥{{ stats.totalAmount }}</div>
                    <div class="stat-label">总消费</div>
                  </div>
                  <el-icon class="stat-icon amount"><Money /></el-icon>
                </el-card>
              </el-col>
            </el-row>

            <!-- 最近订单 -->
            <el-card class="recent-orders" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>最近订单</span>
                  <el-button type="text" @click="$router.push('/orders')">
                    查看全部
                  </el-button>
                </div>
              </template>
              <div v-if="recentOrders.length > 0" class="orders-list">
                <div 
                  v-for="order in recentOrders" 
                  :key="order.id"
                  class="order-item"
                >
                  <div class="order-info">
                    <div class="order-number">{{ order.order_no }}</div>
                    <div class="order-date">{{ formatDate(order.created_at) }}</div>
                  </div>
                  <div class="order-amount">¥{{ order.total_amount }}</div>
                  <el-tag :type="getStatusType(order.status)" size="small">
                    {{ getStatusText(order.status) }}
                  </el-tag>
                </div>
              </div>
              <el-empty v-else description="暂无订单" />
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>

    <!-- 编辑资料对话框 -->
    <el-dialog v-model="editProfileVisible" title="编辑资料" width="500px">
      <el-form :model="profileForm" label-width="80px">
        <el-form-item label="用户名">
          <el-input v-model="profileForm.username" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="profileForm.email" type="email" />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="profileForm.phone" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editProfileVisible = false">取消</el-button>
        <el-button type="primary" @click="saveProfile">保存</el-button>
      </template>
    </el-dialog>

    <!-- 收货地址对话框 -->
    <el-dialog v-model="addressVisible" title="收货地址" width="600px">
      <div class="address-list">
        <div 
          v-for="address in addresses" 
          :key="address.id"
          class="address-item"
        >
          <div class="address-info">
            <div class="address-name">{{ address.name }} {{ address.phone }}</div>
            <div class="address-detail">{{ address.detail }}</div>
          </div>
          <div class="address-actions">
            <el-button type="text" size="small">编辑</el-button>
            <el-button type="text" size="small">删除</el-button>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button type="primary">添加新地址</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../stores/auth'

const userStore = useAuthStore()

// 响应式数据
const editProfileVisible = ref(false)
const addressVisible = ref(false)
const stats = ref({
  totalOrders: 0,
  pendingOrders: 0,
  completedOrders: 0,
  totalAmount: 0
})
const recentOrders = ref([])
const profileForm = ref({
  username: '',
  email: '',
  phone: ''
})
const addresses = ref([])

// 生命周期
onMounted(() => {
  loadUserStats()
  loadRecentOrders()
  loadAddresses()
  initProfileForm()
})

// 方法
const loadUserStats = async () => {
  // 模拟API调用
  stats.value = {
    totalOrders: 12,
    pendingOrders: 2,
    completedOrders: 8,
    totalAmount: 1299.99
  }
}

const loadRecentOrders = async () => {
  // 模拟最近订单数据
  recentOrders.value = [
    {
      id: 1,
      order_no: 'ORD202501100001',
      status: 'completed',
      total_amount: 99.99,
      created_at: '2025-01-10 10:30:00'
    },
    {
      id: 2,
      order_no: 'ORD202501090001',
      status: 'pending',
      total_amount: 199.99,
      created_at: '2025-01-09 15:20:00'
    }
  ]
}

const loadAddresses = async () => {
  // 模拟地址数据
  addresses.value = [
    {
      id: 1,
      name: '张三',
      phone: '13800138000',
      detail: '北京市朝阳区xxx街道xxx号'
    }
  ]
}

const initProfileForm = () => {
  profileForm.value = {
    username: userStore.user?.username || '',
    email: userStore.user?.email || '',
    phone: userStore.user?.phone || ''
  }
}

const saveProfile = async () => {
  try {
    // 这里应该调用API保存用户信息
    ElMessage.success('保存成功')
    editProfileVisible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'info',
    shipped: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待付款',
    paid: '待发货',
    shipped: '待收货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知状态'
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 80px !important;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.profile-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.user-card {
  margin-bottom: 24px;
}

.user-info {
  text-align: center;
}

.user-avatar {
  margin-bottom: 16px;
  background: #409eff;
}

.username {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.user-email {
  margin: 0 0 16px 0;
  color: #909399;
}

.quick-actions {
  margin-bottom: 24px;
}

.action-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.action-item:hover {
  background: #f5f7fa;
}

.action-item .arrow {
  margin-left: auto;
  color: #c0c4cc;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 32px;
  opacity: 0.3;
}

.stat-icon.orders { color: #409eff; }
.stat-icon.pending { color: #e6a23c; }
.stat-icon.completed { color: #67c23a; }
.stat-icon.amount { color: #f56c6c; }

.recent-orders {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
}

.order-info {
  flex: 1;
}

.order-number {
  font-weight: 500;
  color: #303133;
}

.order-date {
  font-size: 12px;
  color: #909399;
}

.order-amount {
  font-weight: 600;
  color: #f56c6c;
  margin-right: 12px;
}

.address-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.address-info {
  flex: 1;
}

.address-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.address-detail {
  color: #606266;
  font-size: 14px;
}

.address-actions {
  display: flex;
  gap: 8px;
}

@media (max-width: 768px) {
  .stats-row {
    margin-bottom: 16px;
  }
  
  .order-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .order-amount {
    margin-right: 0;
  }
}
</style>
