<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情页面优化演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .before, .after {
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #e1e8ed;
        }
        
        .before {
            background: #fff5f5;
            border-color: #fed7d7;
        }
        
        .after {
            background: #f0fff4;
            border-color: #9ae6b4;
        }
        
        .section-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2d3748;
        }
        
        .before .section-title {
            color: #c53030;
        }
        
        .after .section-title {
            color: #38a169;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .quantity-demo {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .old-quantity {
            display: flex;
            gap: 5px;
        }
        
        .old-quantity button {
            width: 30px;
            height: 30px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
        }
        
        .old-quantity input {
            width: 50px;
            height: 30px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        .new-quantity {
            display: flex;
            align-items: center;
            border: 2px solid #e4e7ed;
            border-radius: 12px;
            overflow: hidden;
            background: white;
        }
        
        .new-quantity button {
            width: 40px;
            height: 40px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .new-quantity button:hover {
            background: #409eff;
            color: white;
        }
        
        .new-quantity .display {
            min-width: 60px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-left: 1px solid #e4e7ed;
            border-right: 1px solid #e4e7ed;
            font-weight: 600;
        }
        
        .stock-hint {
            font-size: 14px;
            color: #909399;
            background: #f0f2f5;
            padding: 4px 8px;
            border-radius: 6px;
            margin-left: 10px;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .highlight h3 {
            margin: 0 0 10px 0;
            color: #2d3748;
        }
        
        .tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        
        .tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .tag.new {
            background: #48bb78;
            color: white;
        }
        
        .tag.hot {
            background: #f56565;
            color: white;
        }
        
        .tag.low-stock {
            background: #ed8936;
            color: white;
        }
        
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛍️ 商品详情页面优化演示</h1>
        
        <div class="highlight">
            <h3>✨ 全面提升用户购物体验</h3>
            <p>通过现代化设计和交互优化，打造更优秀的商品详情页面</p>
        </div>
        
        <div class="comparison">
            <div class="before">
                <div class="section-title">🔴 优化前</div>
                <ul class="feature-list">
                    <li>简单的图片展示</li>
                    <li>基础的商品信息</li>
                    <li>普通的数量选择器</li>
                    <li>缺少用户评价</li>
                    <li>没有服务保障信息</li>
                    <li>移动端体验一般</li>
                </ul>
                
                <div class="quantity-demo">
                    <span>数量：</span>
                    <div class="old-quantity">
                        <button>-</button>
                        <input value="1" readonly>
                        <button>+</button>
                    </div>
                </div>
            </div>
            
            <div class="after">
                <div class="section-title">🟢 优化后</div>
                <ul class="feature-list">
                    <li>✅ 图片导航和懒加载</li>
                    <li>✅ 丰富的商品信息展示</li>
                    <li>✅ 现代化数量选择器</li>
                    <li>✅ 用户评价和评分系统</li>
                    <li>✅ 完善的服务保障信息</li>
                    <li>✅ 优秀的移动端体验</li>
                </ul>
                
                <div class="quantity-demo">
                    <span>数量：</span>
                    <div class="new-quantity">
                        <button>−</button>
                        <div class="display">1</div>
                        <button>+</button>
                    </div>
                    <span class="stock-hint">库存10件</span>
                </div>
            </div>
        </div>
        
        <div class="highlight">
            <h3>🏷️ 新增功能特性</h3>
            <div class="tags">
                <span class="tag new">🌟 新品</span>
                <span class="tag hot">🔥 热销</span>
                <span class="tag low-stock">⚠️ 库存紧张</span>
            </div>
            <p>智能标签系统，帮助用户快速了解商品状态</p>
        </div>
        
        <div class="comparison">
            <div class="before">
                <div class="section-title">原有功能</div>
                <ul class="feature-list">
                    <li>商品基本信息</li>
                    <li>价格显示</li>
                    <li>加入购物车</li>
                    <li>立即购买</li>
                </ul>
            </div>
            
            <div class="after">
                <div class="section-title">新增功能</div>
                <ul class="feature-list">
                    <li>🌟 商品评分和评价</li>
                    <li>🚚 配送信息展示</li>
                    <li>🛡️ 服务保障说明</li>
                    <li>📱 分享和收藏功能</li>
                    <li>🔍 商品详情标签页</li>
                    <li>💡 相关商品推荐</li>
                </ul>
            </div>
        </div>
        
        <div class="highlight">
            <h3>📱 移动端优化</h3>
            <p>针对移动端用户习惯，优化了触摸交互和页面布局，提供更好的移动购物体验</p>
        </div>
        
        <div class="highlight">
            <h3>🎨 设计系统升级</h3>
            <p>采用现代化的设计语言，包括渐变背景、圆角设计、阴影系统和流畅的动画效果</p>
        </div>
    </div>
</body>
</html>
