import { defineStore } from 'pinia'
import request from '../utils/request'

export const useGoodsStore = defineStore('goods', {
  state: () => ({
    goodsList: [],
    categories: [],
    currentGoods: null,
    loading: false,
    cart: JSON.parse(localStorage.getItem('cart') || '[]')
  }),

  getters: {
    cartCount: (state) => state.cart.reduce((total, item) => total + item.quantity, 0),
    cartTotal: (state) => state.cart.reduce((total, item) => total + (item.price * item.quantity), 0)
  },

  actions: {
    // 获取商品分类列表
    async fetchCategories() {
      try {
        this.loading = true
        const response = await request.get('/home/<USER>/list')

        if (response.errno === 0) {
          this.categories = response.data
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('获取分类失败:', error)
        return { success: false, message: error.message || '获取分类失败' }
      } finally {
        this.loading = false
      }
    },

    // 获取商品列表
    async fetchGoodsList(params = {}) {
      try {
        this.loading = true
        const response = await request.get('/home/<USER>/list', { params })

        if (response.errno === 0) {
          if (params.last_id) {
            // 加载更多，追加到现有列表
            this.goodsList = [...this.goodsList, ...response.data]
          } else {
            // 新查询，替换列表
            this.goodsList = response.data
          }
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('获取商品列表失败:', error)
        return { success: false, message: error.message || '获取商品列表失败' }
      } finally {
        this.loading = false
      }
    },

    // 获取商品详情
    async fetchGoodsDetail(id) {
      try {
        this.loading = true
        const response = await request.get('/home/<USER>', { params: { id } })

        if (response.errno === 0) {
          this.currentGoods = response.data
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('获取商品详情失败:', error)
        return { success: false, message: error.message || '获取商品详情失败' }
      } finally {
        this.loading = false
      }
    },

    // 获取商品相册
    async fetchGoodsAlbum(goodsId) {
      try {
        const response = await request.get('/home/<USER>/album', { params: { goods_id: goodsId } })

        if (response.errno === 0) {
          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.errmsg }
        }
      } catch (error) {
        console.error('获取商品相册失败:', error)
        return { success: false, message: error.message || '获取商品相册失败' }
      }
    },

    // 添加到购物车
    addToCart(goods, quantity = 1) {
      const existingItem = this.cart.find(item => item.id === goods.id)
      
      if (existingItem) {
        existingItem.quantity += quantity
      } else {
        this.cart.push({
          id: goods.id,
          name: goods.name,
          price: goods.price,
          picture: goods.picture,
          quantity: quantity
        })
      }
      
      this.saveCart()
    },

    // 更新购物车商品数量
    updateCartQuantity(goodsId, quantity) {
      const item = this.cart.find(item => item.id === goodsId)
      if (item) {
        if (quantity <= 0) {
          this.removeFromCart(goodsId)
        } else {
          item.quantity = quantity
          this.saveCart()
        }
      }
    },

    // 从购物车移除商品
    removeFromCart(goodsId) {
      const index = this.cart.findIndex(item => item.id === goodsId)
      if (index > -1) {
        this.cart.splice(index, 1)
        this.saveCart()
      }
    },

    // 清空购物车
    clearCart() {
      this.cart = []
      this.saveCart()
    },

    // 保存购物车到localStorage
    saveCart() {
      localStorage.setItem('cart', JSON.stringify(this.cart))
    }
  }
})
