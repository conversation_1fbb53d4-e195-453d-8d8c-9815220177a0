<template>
  <div class="category-page">
    <el-container>
      <!-- 页面头部 -->
      <el-header class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <span class="category-icon">{{ getCategoryIcon(categoryInfo.name) }}</span>
            {{ categoryInfo.name || '商品分类' }}
          </h1>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ name: 'Home' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>{{ categoryInfo.name || '分类' }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="category-main">
        <!-- 分类信息 -->
        <el-card class="category-info-card" shadow="never" v-if="categoryInfo.name">
          <div class="category-banner">
            <div class="category-details">
              <h2 class="category-title">{{ categoryInfo.name }}</h2>
              <p class="category-description">{{ categoryInfo.description || '精选优质商品，品质保证' }}</p>
              <div class="category-stats">
                <el-tag type="info">共 {{ total }} 件商品</el-tag>
                <el-tag type="success">热销分类</el-tag>
              </div>
            </div>
            <div class="category-image">
              <span class="large-icon">{{ getCategoryIcon(categoryInfo.name) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 筛选和排序 -->
        <el-card class="filter-card" shadow="never">
          <div class="filter-bar">
            <div class="filter-section">
              <span class="filter-label">价格区间：</span>
              <el-button-group class="price-filters">
                <el-button 
                  v-for="range in priceRanges"
                  :key="range.value"
                  :type="selectedPriceRange === range.value ? 'primary' : 'default'"
                  size="small"
                  @click="selectPriceRange(range.value)"
                >
                  {{ range.label }}
                </el-button>
              </el-button-group>
            </div>
            
            <div class="sort-section">
              <span class="sort-label">排序：</span>
              <el-select v-model="sortBy" size="small" style="width: 120px" @change="handleSort">
                <el-option label="综合" value="default" />
                <el-option label="价格升序" value="price_asc" />
                <el-option label="价格降序" value="price_desc" />
                <el-option label="销量" value="sales" />
                <el-option label="最新" value="newest" />
              </el-select>
            </div>
          </div>
        </el-card>

        <!-- 商品列表 -->
        <div class="goods-section">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="6" animated />
          </div>
          
          <!-- 商品网格 -->
          <div v-else-if="goodsList.length > 0" class="goods-grid">
            <el-card 
              v-for="goods in goodsList" 
              :key="goods.id"
              class="goods-card"
              shadow="hover"
              @click="goToDetail(goods.id)"
            >
              <template #header>
                <div class="goods-image-container">
                  <el-image 
                    :src="goods.picture" 
                    :alt="goods.name" 
                    class="goods-image"
                    fit="cover"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                  
                  <!-- 商品标签 -->
                  <div class="goods-tags">
                    <el-tag v-if="goods.is_new" type="success" size="small">新品</el-tag>
                    <el-tag v-if="goods.is_hot" type="danger" size="small">热销</el-tag>
                  </div>
                </div>
              </template>
              
              <div class="goods-content">
                <h3 class="goods-name">{{ goods.name }}</h3>
                
                <div class="goods-price-section">
                  <span class="goods-price">¥{{ goods.price }}</span>
                  <span class="goods-sales">已售{{ goods.sales || 0 }}件</span>
                </div>
                
                <p class="goods-description">{{ truncateDescription(goods.description) }}</p>
                
                <div class="goods-footer">
                  <div class="goods-stock">
                    <el-icon><Box /></el-icon>
                    库存: {{ goods.stock || 0 }}
                  </div>
                  
                  <el-button 
                    type="primary" 
                    size="small"
                    @click.stop="addToCart(goods)"
                    :disabled="!goods.stock"
                  >
                    <el-icon><ShoppingCart /></el-icon>
                    加入购物车
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>
          
          <!-- 空状态 -->
          <el-empty v-else description="该分类下暂无商品">
            <el-button type="primary" @click="$router.push('/')">
              浏览其他分类
            </el-button>
          </el-empty>

          <!-- 分页 -->
          <el-pagination
            v-if="total > 0"
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[12, 24, 48]"
            layout="total, sizes, prev, pager, next, jumper"
            class="pagination"
            @size-change="fetchGoods"
            @current-change="fetchGoods"
          />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()

// 响应式数据
const categoryInfo = ref({})
const goodsList = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)
const sortBy = ref('default')
const selectedPriceRange = ref('')

// 静态数据
const priceRanges = ref([
  { label: '全部', value: '' },
  { label: '0-50', value: '0-50' },
  { label: '50-100', value: '50-100' },
  { label: '100-200', value: '100-200' },
  { label: '200以上', value: '200+' }
])

// 生命周期
onMounted(() => {
  fetchCategoryInfo()
  fetchGoods()
})

// 监听路由变化
watch(() => route.params.id, () => {
  if (route.params.id) {
    fetchCategoryInfo()
    fetchGoods()
  }
})

// 方法
const fetchCategoryInfo = async () => {
  try {
    const categoryId = route.params.id
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟分类信息
    const categories = {
      '1': { name: '水果', description: '新鲜水果，营养丰富，口感甜美' },
      '2': { name: '蔬菜', description: '有机蔬菜，绿色健康，品质保证' },
      '3': { name: '肉类', description: '优质肉类，新鲜安全，营养丰富' },
      '4': { name: '海鲜', description: '新鲜海鲜，味道鲜美，营养价值高' },
      '5': { name: '饮料', description: '各类饮品，解渴提神，口味丰富' }
    }
    
    categoryInfo.value = categories[categoryId] || { name: '未知分类' }
  } catch (error) {
    ElMessage.error('获取分类信息失败')
  }
}

const fetchGoods = async () => {
  loading.value = true
  
  try {
    const categoryId = route.params.id
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟商品数据
    goodsList.value = [
      {
        id: 1,
        name: '新鲜苹果',
        picture: 'https://via.placeholder.com/200',
        price: 29.99,
        description: '新鲜甜美的红富士苹果，营养丰富，口感脆甜',
        stock: 100,
        sales: 256,
        is_new: true,
        is_hot: false
      },
      {
        id: 2,
        name: '有机香蕉',
        picture: 'https://via.placeholder.com/200',
        price: 19.99,
        description: '进口有机香蕉，口感香甜，营养价值高',
        stock: 80,
        sales: 189,
        is_new: false,
        is_hot: true
      }
    ]
    
    total.value = goodsList.value.length
  } catch (error) {
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

const selectPriceRange = (range) => {
  selectedPriceRange.value = range
  currentPage.value = 1
  fetchGoods()
}

const handleSort = () => {
  currentPage.value = 1
  fetchGoods()
}

const getCategoryIcon = (categoryName) => {
  const iconMap = {
    '水果': '🍎',
    '蔬菜': '🥬', 
    '肉类': '🥩',
    '海鲜': '🦐',
    '饮料': '🥤',
    '零食': '🍿',
    '日用品': '🧴',
    '电子产品': '📱',
    '服装': '👕',
    '图书': '📚'
  }
  return iconMap[categoryName] || '🏷️'
}

const truncateDescription = (description) => {
  if (!description) return ''
  return description.length > 60 ? description.substring(0, 60) + '...' : description
}

const goToDetail = (goodsId) => {
  router.push(`/goods/${goodsId}`)
}

const addToCart = (goods) => {
  ElMessage.success('已添加到购物车')
}
</script>

<style scoped>
.category-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 80px !important;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-icon {
  font-size: 28px;
}

.category-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.category-info-card {
  margin-bottom: 24px;
}

.category-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

.category-details {
  flex: 1;
}

.category-title {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 700;
  color: #303133;
}

.category-description {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
}

.category-stats {
  display: flex;
  gap: 8px;
}

.category-image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.large-icon {
  font-size: 80px;
  opacity: 0.8;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-section,
.sort-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label,
.sort-label {
  color: #606266;
  font-size: 14px;
}

.goods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.goods-card {
  cursor: pointer;
  transition: all 0.3s;
}

.goods-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.goods-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.goods-image {
  width: 100%;
  height: 100%;
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f5f7fa;
  color: #c0c4cc;
  font-size: 24px;
}

.goods-tags {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  gap: 4px;
}

.goods-content {
  padding: 16px;
}

.goods-name {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.goods-price {
  font-size: 20px;
  font-weight: 700;
  color: #f56c6c;
}

.goods-sales {
  font-size: 12px;
  color: #909399;
}

.goods-description {
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-stock {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.loading-container {
  padding: 40px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

@media (max-width: 768px) {
  .category-banner {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .filter-bar {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .goods-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }
  
  .category-title {
    font-size: 24px;
  }
  
  .large-icon {
    font-size: 60px;
  }
}
</style>
