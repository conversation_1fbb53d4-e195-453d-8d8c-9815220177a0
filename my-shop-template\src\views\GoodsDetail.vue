<template>
  <div class="goods-detail-page">
    <el-container>
      <!-- 页面头部 -->
      <el-header class="page-header">
        <div class="header-content">
          <el-button @click="goBack" type="info" :icon="ArrowLeft">
            返回
          </el-button>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ name: 'Home' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>商品详情</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="detail-main">
        <!-- 加载状态 -->
        <div v-if="goodsStore.loading" class="loading-container">
          <el-skeleton :rows="8" animated />
        </div>

        <!-- 商品详情 -->
        <div v-else-if="goodsStore.currentGoods" class="detail-content">
          <el-row :gutter="40">
            <!-- 左侧图片区域 -->
            <el-col :xs="24" :md="12">
              <el-card class="image-card" shadow="hover">
                <div class="main-image-container">
                  <el-image
                    :src="mainImage || goodsStore.currentGoods.picture"
                    :alt="goodsStore.currentGoods.name"
                    class="main-image"
                    fit="cover"
                    :preview-src-list="[mainImage || goodsStore.currentGoods.picture]"
                    preview-teleported
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <p>图片加载失败</p>
                      </div>
                    </template>
                  </el-image>

                  <!-- 商品标签 -->
                  <div class="goods-tags">
                    <el-tag v-if="goodsStore.currentGoods.is_new" type="success">新品</el-tag>
                    <el-tag v-if="goodsStore.currentGoods.is_hot" type="danger">热销</el-tag>
                  </div>
                </div>

                <!-- 相册缩略图 -->
                <div v-if="album.length" class="album-thumbnails">
                  <div class="thumbnail-list">
                    <div
                      class="thumbnail-item"
                      :class="{ active: mainImage === goodsStore.currentGoods.picture }"
                      @click="changeMainImage(goodsStore.currentGoods.picture)"
                    >
                      <el-image
                        :src="goodsStore.currentGoods.picture"
                        class="thumbnail-image"
                        fit="cover"
                      />
                    </div>
                    <div
                      v-for="(image, index) in album"
                      :key="index"
                      class="thumbnail-item"
                      :class="{ active: mainImage === image.picture }"
                      @click="changeMainImage(image.picture)"
                    >
                      <el-image
                        :src="image.picture"
                        class="thumbnail-image"
                        fit="cover"
                      />
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>

            <!-- 右侧信息区域 -->
            <el-col :xs="24" :md="12">
              <el-card class="info-card" shadow="hover">
                <div class="goods-info">
                  <h1 class="goods-name">{{ goodsStore.currentGoods.name }}</h1>

                  <div class="price-section">
                    <span class="current-price">¥{{ goodsStore.currentGoods.price }}</span>
                    <span class="sales-info">已售{{ goodsStore.currentGoods.sales || 0 }}件</span>
                  </div>

                  <el-divider />

                  <div class="goods-description">
                    <h3>商品描述</h3>
                    <p>{{ goodsStore.currentGoods.description }}</p>
                  </div>

                  <div v-if="goodsStore.currentGoods.spec" class="goods-spec">
                    <h3>商品规格</h3>
                    <el-tag type="info">{{ goodsStore.currentGoods.spec }}</el-tag>
                  </div>

                  <div class="stock-info">
                    <el-icon><Box /></el-icon>
                    <span>库存：{{ goodsStore.currentGoods.stock }}件</span>
                  </div>

                  <el-divider />

                  <!-- 数量选择器 -->
                  <div class="quantity-section">
                    <span class="quantity-label">购买数量：</span>
                    <el-input-number
                      v-model="quantity"
                      :min="1"
                      :max="goodsStore.currentGoods.stock"
                      size="large"
                      class="quantity-input"
                    />
                  </div>

                  <!-- 操作按钮 -->
                  <div class="action-buttons">
                    <el-button
                      type="warning"
                      size="large"
                      @click="addToCart"
                      :disabled="goodsStore.currentGoods.stock <= 0"
                      class="add-cart-btn"
                    >
                      <el-icon><ShoppingCart /></el-icon>
                      加入购物车
                    </el-button>
                    <el-button
                      type="primary"
                      size="large"
                      @click="buyNow"
                      :disabled="goodsStore.currentGoods.stock <= 0"
                      class="buy-now-btn"
                    >
                      <el-icon><CreditCard /></el-icon>
                      立即购买
                    </el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 错误状态 -->
        <el-empty v-else description="商品不存在或已下架">
          <el-button type="primary" @click="$router.push('/')">
            返回首页
          </el-button>
        </el-empty>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useGoodsStore } from '../stores/goods'

const route = useRoute()
const router = useRouter()
const goodsStore = useGoodsStore()

const quantity = ref(1)
const album = ref([])
const mainImage = ref('')

onMounted(async () => {
  const goodsId = route.params.id

  // 获取商品详情
  const result = await goodsStore.fetchGoodsDetail(goodsId)

  if (result.success) {
    mainImage.value = goodsStore.currentGoods.picture

    // 获取商品相册
    const albumResult = await goodsStore.fetchGoodsAlbum(goodsId)
    if (albumResult.success) {
      album.value = albumResult.data
    }
  } else {
    ElMessage.error('获取商品详情失败')
  }
})

const goBack = () => {
  router.go(-1)
}

const changeMainImage = (imageUrl) => {
  mainImage.value = imageUrl
}

const addToCart = () => {
  if (goodsStore.currentGoods.stock <= 0) {
    ElMessage.warning('商品库存不足')
    return
  }

  if (quantity.value > goodsStore.currentGoods.stock) {
    ElMessage.warning('购买数量超过库存')
    return
  }

  goodsStore.addToCart(goodsStore.currentGoods, quantity.value)
  ElMessage.success(`已添加${quantity.value}件商品到购物车`)
}

const buyNow = () => {
  if (goodsStore.currentGoods.stock <= 0) {
    ElMessage.warning('商品库存不足')
    return
  }

  if (quantity.value > goodsStore.currentGoods.stock) {
    ElMessage.warning('购买数量超过库存')
    return
  }

  goodsStore.addToCart(goodsStore.currentGoods, quantity.value)
  ElMessage.success('已添加到购物车，正在跳转...')

  setTimeout(() => {
    router.push('/cart')
  }, 1000)
}
</script>

<style scoped>
.goods-detail-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 80px !important;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.loading-container {
  padding: 40px;
}

.detail-content {
  margin-bottom: 24px;
}

.image-card,
.info-card {
  height: fit-content;
}

.main-image-container {
  position: relative;
  margin-bottom: 16px;
}

.main-image {
  width: 100%;
  height: 400px;
  border-radius: 12px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #f5f7fa;
  color: #c0c4cc;
  border-radius: 12px;
}

.image-error .el-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.goods-tags {
  position: absolute;
  top: 12px;
  left: 12px;
  display: flex;
  gap: 8px;
}

.album-thumbnails {
  margin-top: 16px;
}

.thumbnail-list {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 4px;
}

.thumbnail-item {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s;
}

.thumbnail-item:hover {
  border-color: #409eff;
  transform: scale(1.05);
}

.thumbnail-item.active {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}

.goods-info {
  padding: 24px;
}

.goods-name {
  margin: 0 0 20px 0;
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1.4;
}

.price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.current-price {
  font-size: 32px;
  font-weight: 700;
  color: #f56c6c;
}

.sales-info {
  font-size: 14px;
  color: #909399;
}

.goods-description h3,
.goods-spec h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.goods-description p {
  margin: 0 0 20px 0;
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}

.goods-spec {
  margin-bottom: 20px;
}

.stock-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #67c23a;
  font-weight: 500;
  margin-bottom: 20px;
}

.quantity-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
}

.quantity-label {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.quantity-input {
  width: 120px;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.add-cart-btn,
.buy-now-btn {
  flex: 1;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
}

.add-cart-btn {
  background: #e6a23c;
  border-color: #e6a23c;
}

.add-cart-btn:hover {
  background: #cf9236;
  border-color: #cf9236;
}

.buy-now-btn {
  background: #409eff;
  border-color: #409eff;
}

.buy-now-btn:hover {
  background: #337ecc;
  border-color: #337ecc;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    height: auto;
  }

  .page-header {
    height: auto !important;
  }

  .detail-main {
    padding: 16px;
  }

  .goods-name {
    font-size: 24px;
  }

  .current-price {
    font-size: 28px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .quantity-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .main-image {
    height: 300px;
  }
}
</style>
