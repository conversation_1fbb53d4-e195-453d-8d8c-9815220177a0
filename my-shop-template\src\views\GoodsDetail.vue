<template>
  <div class="goods-detail-page">
    <el-container>
      <!-- 页面头部 -->
      <el-header class="page-header">
        <div class="header-content">
          <el-button @click="goBack" type="info" :icon="ArrowLeft" class="back-btn">
            返回
          </el-button>
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item :to="{ name: 'Home' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>商品详情</el-breadcrumb-item>
          </el-breadcrumb>

          <!-- 分享按钮 -->
          <div class="header-actions">
            <el-button :icon="Share" circle @click="shareProduct" />
            <el-button :icon="Star" circle @click="toggleFavorite" :type="isFavorite ? 'primary' : 'default'" />
          </div>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="detail-main">
        <!-- 加载状态 -->
        <div v-if="goodsStore.loading" class="loading-container">
          <el-skeleton :rows="8" animated />
        </div>

        <!-- 商品详情 -->
        <div v-else-if="goodsStore.currentGoods" class="detail-content">
          <el-row :gutter="40">
            <!-- 左侧图片区域 -->
            <el-col :xs="24" :lg="13">
              <div class="image-section">
                <div class="main-image-container">
                  <el-image
                    :src="mainImage || goodsStore.currentGoods.picture"
                    :alt="goodsStore.currentGoods.name"
                    class="main-image"
                    fit="cover"
                    :preview-src-list="allImages"
                    preview-teleported
                    lazy
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <p>图片加载失败</p>
                      </div>
                    </template>
                  </el-image>

                  <!-- 商品标签 -->
                  <div class="goods-tags">
                    <el-tag v-if="goodsStore.currentGoods.is_new" type="success" effect="dark">
                      <el-icon><Star /></el-icon>
                      新品
                    </el-tag>
                    <el-tag v-if="goodsStore.currentGoods.is_hot" type="danger" effect="dark">
                      <el-icon><TrendCharts /></el-icon>
                      热销
                    </el-tag>
                    <el-tag v-if="goodsStore.currentGoods.stock < 10" type="warning" effect="dark">
                      <el-icon><Warning /></el-icon>
                      库存紧张
                    </el-tag>
                  </div>

                  <!-- 图片导航 -->
                  <div class="image-nav">
                    <el-button
                      :icon="ArrowLeft"
                      circle
                      size="small"
                      @click="prevImage"
                      :disabled="currentImageIndex === 0"
                      class="nav-btn prev-btn"
                    />
                    <el-button
                      :icon="ArrowRight"
                      circle
                      size="small"
                      @click="nextImage"
                      :disabled="currentImageIndex === allImages.length - 1"
                      class="nav-btn next-btn"
                    />
                  </div>
                </div>

                <!-- 相册缩略图 -->
                <div v-if="allImages.length > 1" class="album-thumbnails">
                  <div class="thumbnail-list">
                    <div
                      v-for="(image, index) in allImages"
                      :key="index"
                      class="thumbnail-item"
                      :class="{ active: currentImageIndex === index }"
                      @click="changeMainImage(image, index)"
                    >
                      <el-image
                        :src="image"
                        class="thumbnail-image"
                        fit="cover"
                        lazy
                      />
                      <div class="thumbnail-overlay" v-if="currentImageIndex === index">
                        <el-icon><Check /></el-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>

            <!-- 右侧信息区域 -->
            <el-col :xs="24" :lg="11">
              <div class="info-section">
                <!-- 商品基本信息 -->
                <div class="goods-basic-info">
                  <h1 class="goods-name">{{ goodsStore.currentGoods.name }}</h1>

                  <!-- 评分和销量 -->
                  <div class="rating-sales">
                    <el-rate v-model="rating" disabled show-score text-color="#ff9900" />
                    <span class="sales-info">已售 {{ goodsStore.currentGoods.sales || 0 }} 件</span>
                  </div>

                  <!-- 价格区域 -->
                  <div class="price-section">
                    <div class="price-main">
                      <span class="price-symbol">¥</span>
                      <span class="current-price">{{ goodsStore.currentGoods.price }}</span>
                      <span v-if="originalPrice" class="original-price">¥{{ originalPrice }}</span>
                    </div>
                    <div class="price-benefits">
                      <el-tag type="danger" size="small" v-if="discount">{{ discount }}折</el-tag>
                      <el-tag type="success" size="small">包邮</el-tag>
                      <el-tag type="info" size="small">7天无理由退货</el-tag>
                    </div>
                  </div>

                  <!-- 库存和配送信息 -->
                  <div class="stock-delivery-info">
                    <div class="info-row">
                      <span class="label">库存：</span>
                      <span class="value" :class="{ 'low-stock': goodsStore.currentGoods.stock < 10 }">
                        {{ goodsStore.currentGoods.stock }} 件
                        <el-tag v-if="goodsStore.currentGoods.stock < 10" type="warning" size="small">库存紧张</el-tag>
                      </span>
                    </div>
                    <div class="info-row">
                      <span class="label">配送：</span>
                      <span class="value">
                        <el-icon><Truck /></el-icon>
                        快递配送，预计1-3天送达
                      </span>
                    </div>
                    <div class="info-row">
                      <span class="label">服务：</span>
                      <span class="value">
                        <el-icon><Shield /></el-icon>
                        正品保证 · 售后无忧
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 规格选择 -->
                <div v-if="goodsStore.currentGoods.spec" class="spec-section">
                  <h3 class="section-title">商品规格</h3>
                  <div class="spec-options">
                    <el-tag
                      v-for="spec in specOptions"
                      :key="spec"
                      :type="selectedSpec === spec ? 'primary' : 'info'"
                      class="spec-tag"
                      @click="selectedSpec = spec"
                    >
                      {{ spec }}
                    </el-tag>
                  </div>
                </div>

                <!-- 数量选择和购买 -->
                <div class="purchase-section">
                  <div class="quantity-section">
                    <span class="quantity-label">数量：</span>
                    <div class="quantity-controls">
                      <el-button
                        :icon="Minus"
                        size="small"
                        @click="decreaseQuantity"
                        :disabled="quantity <= 1"
                      />
                      <el-input-number
                        v-model="quantity"
                        :min="1"
                        :max="goodsStore.currentGoods.stock"
                        size="small"
                        controls-position="right"
                        class="quantity-input"
                      />
                      <el-button
                        :icon="Plus"
                        size="small"
                        @click="increaseQuantity"
                        :disabled="quantity >= goodsStore.currentGoods.stock"
                      />
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="action-buttons">
                    <el-button
                      type="warning"
                      size="large"
                      @click="addToCart"
                      :disabled="goodsStore.currentGoods.stock <= 0"
                      :loading="addingToCart"
                      class="add-cart-btn"
                    >
                      <el-icon><ShoppingCart /></el-icon>
                      加入购物车
                    </el-button>
                    <el-button
                      type="primary"
                      size="large"
                      @click="buyNow"
                      :disabled="goodsStore.currentGoods.stock <= 0"
                      :loading="buying"
                      class="buy-now-btn"
                    >
                      <el-icon><CreditCard /></el-icon>
                      立即购买
                    </el-button>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 商品详细信息标签页 -->
          <div class="detail-tabs-section">
            <el-tabs v-model="activeTab" class="detail-tabs">
              <el-tab-pane label="商品详情" name="description">
                <div class="tab-content">
                  <div class="goods-description">
                    <h3>商品描述</h3>
                    <p>{{ goodsStore.currentGoods.description }}</p>

                    <!-- 商品详细信息 -->
                    <div class="detail-info">
                      <h4>商品信息</h4>
                      <el-descriptions :column="2" border>
                        <el-descriptions-item label="商品名称">{{ goodsStore.currentGoods.name }}</el-descriptions-item>
                        <el-descriptions-item label="商品价格">¥{{ goodsStore.currentGoods.price }}</el-descriptions-item>
                        <el-descriptions-item label="商品规格" v-if="goodsStore.currentGoods.spec">{{ goodsStore.currentGoods.spec }}</el-descriptions-item>
                        <el-descriptions-item label="库存数量">{{ goodsStore.currentGoods.stock }}件</el-descriptions-item>
                        <el-descriptions-item label="商品状态">
                          <el-tag :type="goodsStore.currentGoods.stock > 0 ? 'success' : 'danger'">
                            {{ goodsStore.currentGoods.stock > 0 ? '有库存' : '缺货' }}
                          </el-tag>
                        </el-descriptions-item>
                      </el-descriptions>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="用户评价" name="reviews">
                <div class="tab-content">
                  <div class="reviews-summary">
                    <div class="rating-overview">
                      <div class="rating-score">
                        <span class="score">{{ rating }}</span>
                        <el-rate v-model="rating" disabled show-score text-color="#ff9900" />
                      </div>
                      <div class="rating-stats">
                        <p>共 {{ reviewCount }} 条评价</p>
                        <p>好评率 {{ goodRatePercent }}%</p>
                      </div>
                    </div>
                  </div>

                  <div class="reviews-list">
                    <div v-for="review in reviews" :key="review.id" class="review-item">
                      <div class="review-header">
                        <div class="user-info">
                          <el-avatar :src="review.avatar" :size="40">{{ review.username.charAt(0) }}</el-avatar>
                          <span class="username">{{ review.username }}</span>
                        </div>
                        <div class="review-meta">
                          <el-rate v-model="review.rating" disabled size="small" />
                          <span class="review-date">{{ formatDate(review.date) }}</span>
                        </div>
                      </div>
                      <div class="review-content">
                        <p>{{ review.content }}</p>
                      </div>
                    </div>

                    <el-empty v-if="reviews.length === 0" description="暂无评价" />
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="售后保障" name="service">
                <div class="tab-content">
                  <div class="service-info">
                    <div class="service-item">
                      <el-icon class="service-icon"><Shield /></el-icon>
                      <div class="service-content">
                        <h4>正品保证</h4>
                        <p>所有商品均为正品，支持专柜验货</p>
                      </div>
                    </div>
                    <div class="service-item">
                      <el-icon class="service-icon"><Truck /></el-icon>
                      <div class="service-content">
                        <h4>快速配送</h4>
                        <p>全国大部分地区1-3天送达，偏远地区3-7天</p>
                      </div>
                    </div>
                    <div class="service-item">
                      <el-icon class="service-icon"><RefreshRight /></el-icon>
                      <div class="service-content">
                        <h4>7天无理由退货</h4>
                        <p>商品完好情况下，7天内可无理由退货</p>
                      </div>
                    </div>
                    <div class="service-item">
                      <el-icon class="service-icon"><Service /></el-icon>
                      <div class="service-content">
                        <h4>售后服务</h4>
                        <p>专业客服团队，为您提供贴心售后服务</p>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 相关推荐 -->
          <div class="related-products-section">
            <h3 class="section-title">相关推荐</h3>
            <div class="related-products">
              <div v-for="product in relatedProducts" :key="product.id" class="related-product-item">
                <el-image :src="product.picture" class="product-image" fit="cover" @click="goToProduct(product.id)" />
                <div class="product-info">
                  <h4 class="product-name">{{ product.name }}</h4>
                  <p class="product-price">¥{{ product.price }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 错误状态 -->
        <el-empty v-else description="商品不存在或已下架">
          <el-button type="primary" @click="$router.push('/')">
            返回首页
          </el-button>
        </el-empty>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useGoodsStore } from '../stores/goods'
import {
  ArrowLeft,
  ArrowRight,
  Share,
  Star,
  Picture,
  TrendCharts,
  Warning,
  Check,
  Truck,
  Shield,
  Minus,
  Plus,
  ShoppingCart,
  CreditCard,
  RefreshRight,
  Service
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const goodsStore = useGoodsStore()

// 基础数据
const quantity = ref(1)
const album = ref([])
const mainImage = ref('')
const currentImageIndex = ref(0)

// UI状态
const activeTab = ref('description')
const isFavorite = ref(false)
const addingToCart = ref(false)
const buying = ref(false)

// 商品信息
const rating = ref(4.5)
const originalPrice = ref(null)
const discount = ref(null)
const selectedSpec = ref('')

// 评价数据
const reviewCount = ref(128)
const goodRatePercent = ref(95)
const reviews = ref([
  {
    id: 1,
    username: '用户***123',
    avatar: '',
    rating: 5,
    content: '商品质量很好，包装精美，物流速度快，非常满意！',
    date: new Date('2024-01-15')
  },
  {
    id: 2,
    username: '买家***456',
    avatar: '',
    rating: 4,
    content: '性价比不错，商品和描述一致，推荐购买。',
    date: new Date('2024-01-10')
  }
])

// 相关推荐
const relatedProducts = ref([])

// 计算属性
const allImages = computed(() => {
  const images = [goodsStore.currentGoods?.picture].filter(Boolean)
  if (album.value.length > 0) {
    images.push(...album.value.map(item => item.picture))
  }
  return images
})

const specOptions = computed(() => {
  if (!goodsStore.currentGoods?.spec) return []
  return goodsStore.currentGoods.spec.split(',').map(s => s.trim())
})

onMounted(async () => {
  const goodsId = route.params.id

  // 获取商品详情
  const result = await goodsStore.fetchGoodsDetail(goodsId)

  if (result.success) {
    mainImage.value = goodsStore.currentGoods.picture

    // 设置默认规格
    if (specOptions.value.length > 0) {
      selectedSpec.value = specOptions.value[0]
    }

    // 获取商品相册
    const albumResult = await goodsStore.fetchGoodsAlbum(goodsId)
    if (albumResult.success) {
      album.value = albumResult.data
    }

    // 模拟获取相关推荐商品
    await fetchRelatedProducts()
  } else {
    ElMessage.error('获取商品详情失败')
  }
})

// 方法
const goBack = () => {
  router.go(-1)
}

const changeMainImage = (imageUrl, index) => {
  mainImage.value = imageUrl
  currentImageIndex.value = index
}

const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
    mainImage.value = allImages.value[currentImageIndex.value]
  }
}

const nextImage = () => {
  if (currentImageIndex.value < allImages.value.length - 1) {
    currentImageIndex.value++
    mainImage.value = allImages.value[currentImageIndex.value]
  }
}

const shareProduct = () => {
  ElMessage.success('分享链接已复制到剪贴板')
}

const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value
  ElMessage.success(isFavorite.value ? '已添加到收藏' : '已取消收藏')
}

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

const increaseQuantity = () => {
  if (quantity.value < goodsStore.currentGoods.stock) {
    quantity.value++
  }
}

const addToCart = async () => {
  if (goodsStore.currentGoods.stock <= 0) {
    ElMessage.warning('商品库存不足')
    return
  }

  if (quantity.value > goodsStore.currentGoods.stock) {
    ElMessage.warning('购买数量超过库存')
    return
  }

  addingToCart.value = true

  try {
    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟API调用
    goodsStore.addToCart(goodsStore.currentGoods, quantity.value)
    ElMessage.success(`已添加${quantity.value}件商品到购物车`)
  } finally {
    addingToCart.value = false
  }
}

const buyNow = async () => {
  if (goodsStore.currentGoods.stock <= 0) {
    ElMessage.warning('商品库存不足')
    return
  }

  if (quantity.value > goodsStore.currentGoods.stock) {
    ElMessage.warning('购买数量超过库存')
    return
  }

  buying.value = true

  try {
    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟API调用
    goodsStore.addToCart(goodsStore.currentGoods, quantity.value)
    ElMessage.success('已添加到购物车，正在跳转...')

    setTimeout(() => {
      router.push('/cart')
    }, 1000)
  } finally {
    buying.value = false
  }
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('zh-CN').format(date)
}

const goToProduct = (productId) => {
  router.push(`/goods/${productId}`)
}

const fetchRelatedProducts = async () => {
  // 模拟获取相关推荐商品
  relatedProducts.value = [
    { id: 2, name: '新鲜苹果', price: 12.99, picture: '/api/static/images/apple.jpg' },
    { id: 3, name: '优质香蕉', price: 8.99, picture: '/api/static/images/banana.jpg' },
    { id: 4, name: '甜美草莓', price: 15.99, picture: '/api/static/images/strawberry.jpg' },
    { id: 5, name: '清脆梨子', price: 10.99, picture: '/api/static/images/pear.jpg' }
  ]
}
</script>

<style scoped>
/* 全局样式 */
.goods-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: 80px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-btn {
  border-radius: 20px;
  padding: 8px 16px;
}

.breadcrumb {
  flex: 1;
  margin: 0 24px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 主要内容区域 */
.detail-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 32px 24px;
}

.loading-container {
  padding: 60px;
  text-align: center;
}

.detail-content {
  margin-bottom: 40px;
}

/* 图片区域 */
.image-section {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.image-section:hover {
  transform: translateY(-4px);
}

.main-image-container {
  position: relative;
  margin-bottom: 20px;
  border-radius: 16px;
  overflow: hidden;
}

.main-image {
  width: 100%;
  height: 500px;
  border-radius: 16px;
  transition: transform 0.3s ease;
}

.main-image:hover {
  transform: scale(1.02);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e3e7f0 100%);
  color: #c0c4cc;
  border-radius: 16px;
}

.image-error .el-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.goods-tags {
  position: absolute;
  top: 16px;
  left: 16px;
  display: flex;
  gap: 8px;
  z-index: 10;
}

.goods-tags .el-tag {
  backdrop-filter: blur(10px);
  border: none;
  font-weight: 600;
  padding: 8px 12px;
  border-radius: 20px;
}

.image-nav {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 16px;
  pointer-events: none;
}

.nav-btn {
  pointer-events: all;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.album-thumbnails {
  margin-top: 20px;
}

.thumbnail-list {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 8px 4px;
  scrollbar-width: thin;
}

.thumbnail-list::-webkit-scrollbar {
  height: 4px;
}

.thumbnail-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.thumbnail-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.thumbnail-item {
  position: relative;
  flex-shrink: 0;
  width: 90px;
  height: 90px;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.3s ease;
}

.thumbnail-item:hover {
  border-color: var(--el-color-primary);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.thumbnail-item.active {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}

.thumbnail-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(64, 158, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

/* 商品信息区域 */
.info-section {
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 100px;
}

.goods-basic-info {
  margin-bottom: 32px;
}

.goods-name {
  margin: 0 0 16px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.3;
  letter-spacing: -0.5px;
}

.rating-sales {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.sales-info {
  font-size: 14px;
  color: #666;
  background: #f8f9fa;
  padding: 4px 12px;
  border-radius: 12px;
}

.price-section {
  margin-bottom: 24px;
}

.price-main {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 12px;
}

.price-symbol {
  font-size: 24px;
  font-weight: 600;
  color: #ff4757;
}

.current-price {
  font-size: 42px;
  font-weight: 700;
  color: #ff4757;
  line-height: 1;
}

.original-price {
  font-size: 18px;
  color: #999;
  text-decoration: line-through;
}

.price-benefits {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.stock-delivery-info {
  background: #f8f9fa;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row .label {
  font-weight: 600;
  color: #333;
  min-width: 60px;
}

.info-row .value {
  color: #666;
  display: flex;
  align-items: center;
  gap: 8px;
}

.low-stock {
  color: #ff4757 !important;
  font-weight: 600;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.spec-section {
  margin-bottom: 32px;
}

.spec-options {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.spec-tag {
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.spec-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.purchase-section {
  border-top: 1px solid #eee;
  padding-top: 24px;
}

.quantity-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.quantity-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  min-width: 60px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-input {
  width: 100px;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.add-cart-btn,
.buy-now-btn {
  flex: 1;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 28px;
  transition: all 0.3s ease;
}

.add-cart-btn {
  background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
  border: none;
  color: white;
}

.add-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 167, 38, 0.4);
}

.buy-now-btn {
  background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
  border: none;
  color: white;
}

.buy-now-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(66, 165, 245, 0.4);
}

/* 详情标签页 */
.detail-tabs-section {
  margin-top: 40px;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.detail-tabs {
  border: none;
}

.detail-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: #f8f9fa;
  padding: 0 32px;
}

.detail-tabs :deep(.el-tabs__nav-wrap) {
  background: transparent;
}

.detail-tabs :deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 600;
  padding: 20px 24px;
  color: #666;
}

.detail-tabs :deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
}

.tab-content {
  padding: 32px;
}

.goods-description h3 {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin-bottom: 16px;
}

.goods-description p {
  color: #666;
  line-height: 1.8;
  font-size: 16px;
  margin-bottom: 24px;
}

.detail-info {
  margin-top: 32px;
}

.detail-info h4 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

/* 评价样式 */
.reviews-summary {
  background: #f8f9fa;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
}

.rating-overview {
  display: flex;
  align-items: center;
  gap: 32px;
}

.rating-score {
  text-align: center;
}

.rating-score .score {
  display: block;
  font-size: 48px;
  font-weight: 700;
  color: #ff9900;
  line-height: 1;
  margin-bottom: 8px;
}

.rating-stats p {
  margin: 4px 0;
  color: #666;
}

.reviews-list {
  max-height: 600px;
  overflow-y: auto;
}

.review-item {
  border-bottom: 1px solid #eee;
  padding: 24px 0;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.username {
  font-weight: 600;
  color: #333;
}

.review-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.review-date {
  color: #999;
  font-size: 14px;
}

.review-content p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 服务保障 */
.service-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.service-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 16px;
  transition: transform 0.3s ease;
}

.service-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.service-icon {
  font-size: 32px;
  color: var(--el-color-primary);
  margin-top: 4px;
}

.service-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.service-content p {
  color: #666;
  line-height: 1.5;
  margin: 0;
  font-size: 14px;
}

/* 相关推荐 */
.related-products-section {
  margin-top: 40px;
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.related-products {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.related-product-item {
  background: #f8f9fa;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.related-product-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.related-product-item .product-image {
  width: 100%;
  height: 160px;
}

.related-product-item .product-info {
  padding: 16px;
}

.related-product-item .product-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-product-item .product-price {
  font-size: 16px;
  font-weight: 700;
  color: #ff4757;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .detail-main {
    max-width: 100%;
    padding: 24px 16px;
  }

  .info-section {
    position: static;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    height: auto;
  }

  .page-header {
    height: auto !important;
  }

  .breadcrumb {
    margin: 0;
  }

  .header-actions {
    order: -1;
    align-self: flex-end;
  }

  .detail-main {
    padding: 16px;
  }

  .image-section,
  .info-section {
    padding: 20px;
  }

  .main-image {
    height: 300px;
  }

  .goods-name {
    font-size: 24px;
  }

  .current-price {
    font-size: 32px;
  }

  .price-symbol {
    font-size: 20px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .quantity-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .quantity-controls {
    width: 100%;
    justify-content: center;
  }

  .stock-delivery-info {
    padding: 16px;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .tab-content {
    padding: 20px;
  }

  .rating-overview {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .service-info {
    grid-template-columns: 1fr;
  }

  .related-products {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .thumbnail-list {
    gap: 8px;
  }

  .thumbnail-item {
    width: 70px;
    height: 70px;
  }
}

@media (max-width: 480px) {
  .goods-name {
    font-size: 20px;
  }

  .current-price {
    font-size: 28px;
  }

  .main-image {
    height: 250px;
  }

  .related-products {
    grid-template-columns: 1fr;
  }

  .spec-options {
    gap: 8px;
  }

  .spec-tag {
    padding: 6px 12px;
    font-size: 14px;
  }
}
</style>
