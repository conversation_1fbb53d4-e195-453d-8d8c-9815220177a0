<template>
  <div class="dashboard">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <h2 class="logo">商城管理</h2>
      </div>
      
      <nav class="sidebar-nav">
        <router-link to="/" class="nav-item">
          <span class="nav-icon">📊</span>
          <span class="nav-text">仪表板</span>
        </router-link>
        
        <router-link to="/goods" class="nav-item">
          <span class="nav-icon">📦</span>
          <span class="nav-text">商品管理</span>
        </router-link>
        
        <router-link to="/categories" class="nav-item">
          <span class="nav-icon">📂</span>
          <span class="nav-text">分类管理</span>
        </router-link>
        
        <router-link to="/profile" class="nav-item" active-class="active">
          <span class="nav-icon">👤</span>
          <span class="nav-text">个人资料</span>
        </router-link>
      </nav>
    </aside>
    
    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 顶部栏 -->
      <header class="top-bar">
        <div class="top-bar-left">
          <h1 class="page-title">个人资料</h1>
        </div>
        
        <div class="top-bar-right">
          <div class="admin-info">
            <span class="admin-name">{{ authStore.admin?.username || '管理员' }}</span>
            <button @click="logout" class="logout-btn">退出登录</button>
          </div>
        </div>
      </header>
      
      <!-- 内容区域 -->
      <div class="content">
        <div class="profile-container">
          <!-- 管理员信息卡片 -->
          <div class="profile-card">
            <div class="profile-header">
              <div class="avatar-section">
                <img 
                  :src="authStore.admin?.avatar || '/default-avatar.png'" 
                  :alt="authStore.admin?.username"
                  class="avatar"
                >
                <button @click="showAvatarModal = true" class="change-avatar-btn">
                  更换头像
                </button>
              </div>
              
              <div class="admin-details">
                <h2 class="admin-username">{{ authStore.admin?.username || '管理员' }}</h2>
                <p class="admin-role">系统管理员</p>
                <p class="admin-id">ID: {{ authStore.admin?.id }}</p>
              </div>
            </div>
          </div>
          
          <!-- 修改密码卡片 -->
          <div class="password-card">
            <h3 class="card-title">修改密码</h3>
            
            <form @submit.prevent="changePassword" class="password-form">
              <div class="form-group">
                <label>新密码</label>
                <input 
                  v-model="passwordForm.password" 
                  type="password" 
                  required 
                  placeholder="请输入新密码"
                  minlength="6"
                >
              </div>
              
              <div class="form-group">
                <label>确认新密码</label>
                <input 
                  v-model="passwordForm.confirmPassword" 
                  type="password" 
                  required 
                  placeholder="请再次输入新密码"
                  minlength="6"
                >
              </div>
              
              <button 
                type="submit" 
                :disabled="passwordSubmitting"
                class="submit-btn"
              >
                {{ passwordSubmitting ? '修改中...' : '修改密码' }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </main>
    
    <!-- 更换头像模态框 -->
    <div v-if="showAvatarModal" class="modal-overlay" @click="closeAvatarModal">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>更换头像</h3>
          <button @click="closeAvatarModal" class="close-btn">×</button>
        </div>
        
        <form @submit.prevent="changeAvatar" class="modal-form">
          <div class="form-group">
            <label>头像URL</label>
            <input 
              v-model="avatarForm.avatar" 
              type="url" 
              required 
              placeholder="请输入头像图片URL"
            >
          </div>
          
          <div v-if="avatarForm.avatar" class="avatar-preview">
            <img :src="avatarForm.avatar" alt="头像预览" class="preview-image">
          </div>
          
          <div class="form-actions">
            <button type="button" @click="closeAvatarModal" class="cancel-btn">取消</button>
            <button type="submit" :disabled="avatarSubmitting" class="submit-btn">
              {{ avatarSubmitting ? '更新中...' : '确定' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const showAvatarModal = ref(false)
const passwordSubmitting = ref(false)
const avatarSubmitting = ref(false)

const passwordForm = ref({
  password: '',
  confirmPassword: ''
})

const avatarForm = ref({
  avatar: ''
})

onMounted(async () => {
  await authStore.initAuth()
})

const changePassword = async () => {
  if (passwordForm.value.password !== passwordForm.value.confirmPassword) {
    alert('两次输入的密码不一致')
    return
  }
  
  if (passwordForm.value.password.length < 6) {
    alert('密码长度至少6位')
    return
  }
  
  passwordSubmitting.value = true
  
  try {
    const result = await authStore.changePassword(passwordForm.value.password)
    
    if (result.success) {
      alert('密码修改成功')
      passwordForm.value = { password: '', confirmPassword: '' }
    } else {
      alert(result.message || '密码修改失败')
    }
  } catch (error) {
    alert(error.message || '密码修改失败')
  } finally {
    passwordSubmitting.value = false
  }
}

const changeAvatar = async () => {
  avatarSubmitting.value = true
  
  try {
    const result = await authStore.changeAvatar(avatarForm.value.avatar)
    
    if (result.success) {
      alert('头像更新成功')
      closeAvatarModal()
    } else {
      alert(result.message || '头像更新失败')
    }
  } catch (error) {
    alert(error.message || '头像更新失败')
  } finally {
    avatarSubmitting.value = false
  }
}

const closeAvatarModal = () => {
  showAvatarModal.value = false
  avatarForm.value.avatar = ''
}

const logout = () => {
  if (confirm('确定要退出登录吗？')) {
    authStore.logout()
    router.push('/login')
  }
}
</script>

<style scoped>
/* 复用之前的样式 */
.dashboard {
  display: flex;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.sidebar {
  width: 250px;
  background: #2c3e50;
  color: white;
  flex-shrink: 0;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #34495e;
}

.logo {
  font-size: 1.3rem;
  font-weight: bold;
  margin: 0;
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.3s ease;
}

.nav-item:hover,
.nav-item.active {
  background: #34495e;
  color: white;
}

.nav-icon {
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.nav-text {
  font-size: 0.95rem;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.top-bar {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.admin-name {
  font-weight: 500;
  color: #2c3e50;
}

.logout-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.logout-btn:hover {
  background: #c0392b;
}

.content {
  flex: 1;
  padding: 2rem;
}

.profile-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-card,
.password-card {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #ecf0f1;
}

.change-avatar-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.change-avatar-btn:hover {
  background: #2980b9;
}

.admin-details {
  flex: 1;
}

.admin-username {
  font-size: 2rem;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.admin-role {
  color: #27ae60;
  font-weight: 500;
  margin: 0 0 0.5rem 0;
}

.admin-id {
  color: #7f8c8d;
  margin: 0;
}

.card-title {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.password-form {
  max-width: 400px;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.submit-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 1rem;
}

.submit-btn:hover:not(:disabled) {
  background: #0056b3;
}

.submit-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #ecf0f1;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.close-btn:hover {
  color: #2c3e50;
}

.modal-form {
  padding: 1.5rem;
}

.avatar-preview {
  text-align: center;
  margin: 1rem 0;
}

.preview-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #ecf0f1;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.cancel-btn:hover {
  background: #5a6268;
}
</style>
