import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import './style.css'

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('./views/Login.vue'),
    meta: {
      requiresAuth: false,
      title: '管理员登录'
    }
  },
  {
    path: '/',
    name: 'Dashboard',
    component: () => import('./views/Dashboard.vue'),
    meta: {
      requiresAuth: true,
      title: '仪表盘'
    }
  },
  {
    path: '/goods',
    name: 'GoodsManagement',
    component: () => import('./views/GoodsManagement.vue'),
    meta: {
      requiresAuth: true,
      title: '商品管理'
    }
  },
  {
    path: '/categories',
    name: 'CategoryManagement',
    component: () => import('./views/CategoryManagement.vue'),
    meta: {
      requiresAuth: true,
      title: '分类管理'
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('./views/Profile.vue'),
    meta: {
      requiresAuth: true,
      title: '个人资料'
    }
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('./views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 路由切换时的滚动行为
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

const app = createApp(App)
const pinia = createPinia()
app.use(pinia)
app.use(router)

// 全局路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 商城管理系统` : '商城管理系统'

  // 动态导入store以避免循环依赖
  const { useAuthStore } = await import('./stores/auth')
  const authStore = useAuthStore()

  // 如果有token但未初始化认证状态，先初始化
  if (authStore.token && !authStore.isAuthenticated) {
    try {
      await authStore.initAuth()
    } catch (error) {
      console.error('认证初始化失败:', error)
      authStore.logout()
    }
  }

  // 需要登录的路由
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next({
        name: 'Login',
        query: { redirect: to.fullPath } // 保存重定向路径
      })
      return
    }
  }

  // 已登录用户访问登录页，重定向到首页或指定页面
  if (to.name === 'Login' && authStore.isAuthenticated) {
    const redirect = to.query.redirect || '/'
    next(redirect)
    return
  }

  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 可以在这里添加页面访问统计等逻辑
  console.log(`路由跳转: ${from.name || 'unknown'} -> ${to.name || 'unknown'}`)
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
})

app.mount('#app')
